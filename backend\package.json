{"name": "twins-bank-backend", "version": "1.0.0", "description": "Backend API para o sistema bancário Twins_Bank", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["banking", "api", "nodejs", "express", "mysql", "jwt"], "author": "Twins_Bank Development Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.57.4", "axios": "^1.12.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.0", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"eslint": "^8.45.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}