const mysql = require('mysql2/promise');
const logger = require('../core/logger');

// Configuração da conexão com a base de dados
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'twins_bank',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  charset: 'utf8mb4',
  timezone: '+00:00'
};

// Pool de conexões
let pool;

/**
 * Inicializa o pool de conexões com a base de dados com retry logic
 */
const initializeDatabase = async (retries = 5, delay = 2000) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      pool = mysql.createPool(dbConfig);

      // Testar conexão
      const connection = await pool.getConnection();
      await connection.ping();
      connection.release();

      logger.info('Yo<PERSON><PERSON> Carlos, conexão com a base de dados estabelecida com sucesso 👌');
      logger.info(`Base de dados: ${dbConfig.database} em ${dbConfig.host}:${dbConfig.port}`);

      return pool;
    } catch (error) {
      logger.error(`Tentativa ${attempt}/${retries} de conexão com a base de dados falhou:`, {
        error: error.message,
        code: error.code,
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database
      });

      if (attempt === retries) {
        logger.error('Todas as tentativas de conexão falharam. Verifique a configuração da base de dados.');
        throw new Error(`Falha na conexão com a base de dados após ${retries} tentativas: ${error.message}`);
      }

      // Aguardar antes da próxima tentativa
      logger.info(`Aguardando ${delay}ms antes da próxima tentativa...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 1.5; // Aumentar delay exponencialmente
    }
  }
};

/**
 * Executa uma query na base de dados com retry logic
 * @param {string} query - Query SQL
 * @param {Array} params - Parâmetros da query
 * @param {number} retries - Número de tentativas em caso de erro de conexão
 * @returns {Promise} Resultado da query
 */
const executeQuery = async (query, params = [], retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      if (!pool) {
        await initializeDatabase();
      }

      const [results] = await pool.execute(query, params);
      return results;
    } catch (error) {
      const isConnectionError = error.code === 'ECONNRESET' ||
                               error.code === 'ECONNREFUSED' ||
                               error.code === 'ETIMEDOUT' ||
                               error.code === 'PROTOCOL_CONNECTION_LOST';

      logger.error(`Erro ao executar query (tentativa ${attempt}/${retries}):`, {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        params: params.length > 0 ? `[${params.length} parâmetros]` : '[]',
        error: error.message,
        code: error.code,
        sqlState: error.sqlState
      });

      // Se for erro de conexão e ainda há tentativas, tentar novamente
      if (isConnectionError && attempt < retries) {
        logger.warn(`Erro de conexão detectado. Tentando reconectar... (${attempt}/${retries})`);
        pool = null; // Forçar reinicialização do pool
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        continue;
      }

      // Se não for erro de conexão ou esgotaram as tentativas, lançar erro
      throw error;
    }
  }
};

/**
 * Executa múltiplas queries numa transação
 * @param {Array} queries - Array de objetos {query, params}
 * @returns {Promise} Resultado das queries
 */
const executeTransactionQueries = async (queries) => {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();

    const results = [];
    for (const { query, params = [] } of queries) {
      const [result] = await connection.execute(query, params);
      results.push(result);
    }

    await connection.commit();
    return results;
  } catch (error) {
    await connection.rollback();
    logger.error('Erro na transação:', error);
    throw error;
  } finally {
    connection.release();
  }
};

/**
 * Executa uma função dentro de uma transação
 * @param {Function} callback - Função que recebe a conexão como parâmetro
 * @returns {Promise} Resultado da função
 */
const executeTransaction = async (callback) => {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();

    const result = await callback(connection);

    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    logger.error('Erro na transação:', error);
    throw error;
  } finally {
    connection.release();
  }
};

/**
 * Fecha o pool de conexões
 */
const closeDatabase = async () => {
  if (pool) {
    await pool.end();
    logger.info('Pool de conexões fechado');
  }
};

/**
 * Verifica se a base de dados está acessível
 */
const checkDatabaseHealth = async () => {
  try {
    await executeQuery('SELECT 1 as health_check');
    return { status: 'healthy', timestamp: new Date().toISOString() };
  } catch (error) {
    return { 
      status: 'unhealthy', 
      error: error.message, 
      timestamp: new Date().toISOString() 
    };
  }
};

module.exports = {
  initializeDatabase,
  executeQuery,
  executeTransaction,
  executeTransactionQueries,
  closeDatabase,
  checkDatabaseHealth,
  getPool: () => pool
};
