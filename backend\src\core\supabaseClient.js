const { createClient } = require('@supabase/supabase-js');
const logger = require('./logger');

// Configuração do cliente Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  logger.error('Configuração do Supabase incompleta. Verifique as variáveis SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY no arquivo .env');
  throw new Error('Configuração do Supabase incompleta');
}

// Criar cliente Supabase com service role key para operações administrativas
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

/**
 * Gerar nome único para ficheiro
 * @param {string} originalName - Nome original do ficheiro
 * @param {string} prefix - Prefixo para o nome do ficheiro
 * @returns {string} Nome único do ficheiro
 */
function generateUniqueFileName(originalName, prefix = '') {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = originalName.split('.').pop();
  const baseName = originalName.split('.').slice(0, -1).join('.');
  
  return `${prefix}_${timestamp}_${randomString}_${baseName}.${extension}`;
}

/**
 * Fazer upload de ficheiro para Supabase Storage
 * @param {string} bucket - Nome do bucket
 * @param {string} path - Caminho do ficheiro no bucket
 * @param {Buffer} fileBuffer - Buffer do ficheiro
 * @param {Object} options - Opções de upload
 * @returns {Promise<Object>} Resultado do upload
 */
async function uploadFile(bucket, path, fileBuffer, options = {}) {
  try {
    logger.info(`Iniciando upload para Supabase: ${bucket}/${path}`);

    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, fileBuffer, {
        contentType: options.contentType || 'application/octet-stream',
        upsert: options.upsert || false,
        ...options
      });

    if (error) {
      logger.error('Erro no upload para Supabase:', error);
      throw new Error(`Erro no upload: ${error.message}`);
    }

    // Obter URL pública do ficheiro
    const { data: publicUrlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(path);

    logger.info(`Upload concluído com sucesso: ${publicUrlData.publicUrl}`);

    return {
      path: data.path,
      publicUrl: publicUrlData.publicUrl,
      fullPath: data.fullPath,
      id: data.id
    };
  } catch (error) {
    logger.error('Erro no upload de ficheiro:', error);
    throw error;
  }
}

/**
 * Eliminar ficheiro do Supabase Storage
 * @param {string} bucket - Nome do bucket
 * @param {string} path - Caminho do ficheiro no bucket
 * @returns {Promise<boolean>} True se eliminado com sucesso
 */
async function deleteFile(bucket, path) {
  try {
    logger.info(`Eliminando ficheiro do Supabase: ${bucket}/${path}`);

    const { data, error } = await supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) {
      logger.error('Erro ao eliminar ficheiro do Supabase:', error);
      throw new Error(`Erro ao eliminar ficheiro: ${error.message}`);
    }

    logger.info(`Ficheiro eliminado com sucesso: ${bucket}/${path}`);
    return true;
  } catch (error) {
    logger.error('Erro ao eliminar ficheiro:', error);
    throw error;
  }
}

/**
 * Eliminar múltiplos ficheiros do Supabase Storage
 * @param {string} bucket - Nome do bucket
 * @param {string[]} paths - Array de caminhos dos ficheiros
 * @returns {Promise<boolean>} True se todos eliminados com sucesso
 */
async function deleteFiles(bucket, paths) {
  try {
    logger.info(`Eliminando ${paths.length} ficheiros do Supabase: ${bucket}`);

    const { data, error } = await supabase.storage
      .from(bucket)
      .remove(paths);

    if (error) {
      logger.error('Erro ao eliminar ficheiros do Supabase:', error);
      throw new Error(`Erro ao eliminar ficheiros: ${error.message}`);
    }

    logger.info(`${paths.length} ficheiros eliminados com sucesso do bucket: ${bucket}`);
    return true;
  } catch (error) {
    logger.error('Erro ao eliminar ficheiros:', error);
    throw error;
  }
}

/**
 * Eliminar pasta completa do Supabase Storage
 * @param {string} bucket - Nome do bucket
 * @param {string} folderPath - Caminho da pasta
 * @returns {Promise<boolean>} True se eliminada com sucesso
 */
async function deleteFolder(bucket, folderPath) {
  try {
    logger.info(`Eliminando pasta do Supabase: ${bucket}/${folderPath}`);

    // Listar todos os ficheiros na pasta
    const { data: files, error: listError } = await supabase.storage
      .from(bucket)
      .list(folderPath);

    if (listError) {
      logger.error('Erro ao listar ficheiros da pasta:', listError);
      throw new Error(`Erro ao listar ficheiros: ${listError.message}`);
    }

    if (!files || files.length === 0) {
      logger.info(`Pasta vazia ou não encontrada: ${bucket}/${folderPath}`);
      return true;
    }

    // Criar array de caminhos completos
    const filePaths = files.map(file => `${folderPath}/${file.name}`);

    // Eliminar todos os ficheiros
    const { data, error } = await supabase.storage
      .from(bucket)
      .remove(filePaths);

    if (error) {
      logger.error('Erro ao eliminar pasta do Supabase:', error);
      throw new Error(`Erro ao eliminar pasta: ${error.message}`);
    }

    logger.info(`Pasta eliminada com sucesso: ${bucket}/${folderPath} (${filePaths.length} ficheiros)`);
    return true;
  } catch (error) {
    logger.error('Erro ao eliminar pasta:', error);
    throw error;
  }
}

/**
 * Listar ficheiros em um bucket
 * @param {string} bucket - Nome do bucket
 * @param {string} path - Caminho da pasta (opcional)
 * @returns {Promise<Array>} Lista de ficheiros
 */
async function listFiles(bucket, path = '') {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .list(path);

    if (error) {
      logger.error('Erro ao listar ficheiros:', error);
      throw new Error(`Erro ao listar ficheiros: ${error.message}`);
    }

    return data || [];
  } catch (error) {
    logger.error('Erro ao listar ficheiros:', error);
    throw error;
  }
}

/**
 * Obter URL pública de um ficheiro
 * @param {string} bucket - Nome do bucket
 * @param {string} path - Caminho do ficheiro
 * @returns {string} URL pública
 */
function getPublicUrl(bucket, path) {
  const { data } = supabase.storage
    .from(bucket)
    .getPublicUrl(path);

  return data.publicUrl;
}

module.exports = {
  supabase,
  uploadFile,
  deleteFile,
  deleteFiles,
  deleteFolder,
  listFiles,
  getPublicUrl,
  generateUniqueFileName
};
