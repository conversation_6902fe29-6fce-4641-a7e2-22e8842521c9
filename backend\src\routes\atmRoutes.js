const express = require('express');
const { authorize } = require('../auth/middleware');
const { executeQuery } = require('../config/database');
const { AppError } = require('../utils/appError');
const { catchAsync } = require('../utils/catchAsync');
const { v4: uuidv4 } = require('uuid');
const Joi = require('joi');
const logger = require('../utils/logger');

const router = express.Router();

// Schema de validação para carregamento de ATM
const loadATMSchema = Joi.object({
  atm_id: Joi.number().integer().positive().required(),
  amount: Joi.number().positive().max(100000000).required(),
  denominations: Joi.object({
    notes_10000: Joi.number().integer().min(0).default(0),
    notes_5000: Joi.number().integer().min(0).default(0),
    notes_2000: Joi.number().integer().min(0).default(0),
    notes_1000: Joi.number().integer().min(0).default(0),
    notes_500: Joi.number().integer().min(0).default(0),
    notes_200: Joi.number().integer().min(0).default(0),
    notes_100: Joi.number().integer().min(0).default(0),
    notes_50: Joi.number().integer().min(0).default(0),
    coins_10: Joi.number().integer().min(0).default(0),
    coins_5: Joi.number().integer().min(0).default(0),
    coins_1: Joi.number().integer().min(0).default(0)
  }).required(),
  notes: Joi.string().max(500).allow('', null)
});

/**
 * GET /api/atm
 * Listar ATMs com informações detalhadas
 */
router.get('/', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  const atms = await executeQuery(`
    SELECT
      a.id,
      a.atm_code,
      a.location,
      a.cash_capacity,
      a.current_balance,
      a.status,
      a.last_maintenance,
      a.installed_date,
      b.name as branch_name,
      ROUND((a.current_balance / a.cash_capacity) * 100, 2) as capacity_percentage
    FROM atms a
    LEFT JOIN branches b ON a.branch_id = b.id
    ORDER BY a.atm_code
  `);

  res.status(200).json({
    status: 'success',
    data: {
      atms,
      total: atms.length
    }
  });
}));

/**
 * GET /api/atm/:id
 * Obter detalhes de um ATM específico
 */
router.get('/:id', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const atm = await executeQuery(`
    SELECT
      a.id,
      a.atm_code,
      a.location,
      a.cash_capacity,
      a.current_balance,
      a.status,
      a.last_maintenance,
      a.installed_date,
      b.name as branch_name,
      ROUND((a.current_balance / a.cash_capacity) * 100, 2) as capacity_percentage
    FROM atms a
    LEFT JOIN branches b ON a.branch_id = b.id
    WHERE a.id = ?
  `, [id]);

  if (!atm.length) {
    return next(new AppError('ATM não encontrado', 404, 'ATM_NOT_FOUND'));
  }

  res.status(200).json({
    status: 'success',
    data: {
      atm: atm[0]
    }
  });
}));

/**
 * GET /api/atm/:id/loadings
 * Obter histórico de carregamentos de um ATM
 */
router.get('/:id/loadings', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { limit = 10, offset = 0 } = req.query;

  // Verificar se ATM existe
  const atmExists = await executeQuery('SELECT id FROM atms WHERE id = ?', [id]);
  if (!atmExists.length) {
    return next(new AppError('ATM não encontrado', 404, 'ATM_NOT_FOUND'));
  }

  const loadings = await executeQuery(`
    SELECT
      al.id,
      al.loaded_amount,
      al.loading_date,
      al.notes,
      u.full_name as loaded_by_name,
      a.atm_code
    FROM atm_loadings al
    JOIN users u ON al.loaded_by = u.id
    JOIN atms a ON al.atm_id = a.id
    WHERE al.atm_id = ?
    ORDER BY al.loading_date DESC
    LIMIT ? OFFSET ?
  `, [id, parseInt(limit), parseInt(offset)]);

  const totalCount = await executeQuery(
    'SELECT COUNT(*) as total FROM atm_loadings WHERE atm_id = ?',
    [id]
  );

  res.status(200).json({
    status: 'success',
    data: {
      loadings,
      total: totalCount[0].total,
      limit: parseInt(limit),
      offset: parseInt(offset)
    }
  });
}));

/**
 * POST /api/atm/load
 * Carregar ATM com numerário
 */
router.post('/load', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = loadATMSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { atm_id, amount, denominations, notes } = value;

  // 2. Verificar se ATM existe e está online
  const atm = await executeQuery(`
    SELECT id, atm_code, location, cash_capacity, current_balance, status
    FROM atms
    WHERE id = ?
  `, [atm_id]);

  if (!atm.length) {
    return next(new AppError('ATM não encontrado', 404, 'ATM_NOT_FOUND'));
  }

  const atmData = atm[0];

  if (atmData.status === 'offline') {
    return next(new AppError('ATM está offline e não pode ser carregado', 400, 'ATM_OFFLINE'));
  }

  // 3. Verificar se o carregamento não excede a capacidade
  const newBalance = parseFloat(atmData.current_balance) + amount;
  if (newBalance > parseFloat(atmData.cash_capacity)) {
    return next(new AppError(
      `Carregamento excede a capacidade do ATM. Capacidade: ${atmData.cash_capacity} AOA, Saldo atual: ${atmData.current_balance} AOA, Tentativa de carregar: ${amount} AOA`,
      400,
      'CAPACITY_EXCEEDED'
    ));
  }

  // 4. Calcular total das denominações
  const calculatedTotal = (
    denominations.notes_10000 * 10000 +
    denominations.notes_5000 * 5000 +
    denominations.notes_2000 * 2000 +
    denominations.notes_1000 * 1000 +
    denominations.notes_500 * 500 +
    denominations.notes_200 * 200 +
    denominations.notes_100 * 100 +
    denominations.notes_50 * 50 +
    denominations.coins_10 * 10 +
    denominations.coins_5 * 5 +
    denominations.coins_1 * 1
  );

  // 5. Verificar se total das denominações confere com o valor informado
  if (Math.abs(calculatedTotal - amount) > 0.01) {
    return next(new AppError('Total das denominações não confere com o valor informado', 400, 'DENOMINATION_MISMATCH'));
  }

  // 6. Verificar saldo do cofre principal (assumindo que o carregamento vem do cofre)
  const vaultBalance = await executeQuery(`
    SELECT current_balance
    FROM main_vaults
    WHERE branch_id = 1 AND vault_type = 'main'
    ORDER BY created_at DESC
    LIMIT 1
  `);

  if (!vaultBalance.length || parseFloat(vaultBalance[0].current_balance) < amount) {
    return next(new AppError('Saldo insuficiente no cofre principal para realizar o carregamento', 400, 'INSUFFICIENT_VAULT_BALANCE'));
  }

  // 7. Iniciar transação
  await executeQuery('START TRANSACTION');

  try {
    // 8. Criar registro de carregamento
    const loadingId = uuidv4();
    await executeQuery(`
      INSERT INTO atm_loadings (id, atm_id, loaded_amount, loaded_by, notes)
      VALUES (?, ?, ?, ?, ?)
    `, [loadingId, atm_id, amount, req.user.id, notes || null]);

    // 9. Atualizar saldo do ATM
    await executeQuery(`
      UPDATE atms
      SET current_balance = current_balance + ?,
          status = CASE
            WHEN status = 'low_balance' AND (current_balance + ?) > (cash_capacity * 0.2) THEN 'online'
            ELSE status
          END
      WHERE id = ?
    `, [amount, amount, atm_id]);

    // 10. Reduzir saldo do cofre principal
    const currentVaultBalance = parseFloat(vaultBalance[0].current_balance);
    const newVaultBalance = currentVaultBalance - amount;

    await executeQuery(`
      UPDATE main_vaults
      SET current_balance = ?
      WHERE branch_id = 1 AND vault_type = 'main'
    `, [newVaultBalance]);

    // 11. Registrar movimento no cofre
    await executeQuery(`
      INSERT INTO vault_movements (id, vault_id, movement_type, amount, description, created_by)
      SELECT ?, mv.id, 'outgoing', ?, CONCAT('Carregamento ATM ', ?), ?
      FROM main_vaults mv
      WHERE mv.branch_id = 1 AND mv.vault_type = 'main'
      LIMIT 1
    `, [uuidv4(), amount, atmData.atm_code, req.user.id]);

    // 12. Registrar auditoria
    await executeQuery(`
      INSERT INTO audit_logs (id, user_id, action, table_name, record_id, old_values, new_values, ip_address)
      VALUES (?, ?, 'ATM_LOADING', 'atms', ?, ?, ?, ?)
    `, [
      uuidv4(),
      req.user.id,
      JSON.stringify({ current_balance: atmData.current_balance }),
      JSON.stringify({ current_balance: newBalance, loaded_amount: amount }),
      req.ip || 'unknown'
    ]);

    // 13. Confirmar transação
    await executeQuery('COMMIT');

    // 14. Buscar dados atualizados do ATM
    const updatedATM = await executeQuery(`
      SELECT
        a.id,
        a.atm_code,
        a.location,
        a.cash_capacity,
        a.current_balance,
        a.status,
        ROUND((a.current_balance / a.cash_capacity) * 100, 2) as capacity_percentage
      FROM atms a
      WHERE a.id = ?
    `, [atm_id]);

    logger.info(`Carregamento de ATM realizado: ${amount} AOA para ${atmData.atm_code}`, {
      loadingId,
      atmId: atm_id,
      amount,
      loadedBy: req.user.id,
      newBalance
    });

    res.status(201).json({
      status: 'success',
      message: `Carregamento de ${amount.toLocaleString('pt-AO')} AOA realizado com sucesso no ${atmData.atm_code}`,
      data: {
        loading: {
          id: loadingId,
          atm_id,
          loaded_amount: amount,
          loading_date: new Date(),
          notes
        },
        atm: updatedATM[0]
      }
    });

  } catch (error) {
    // Rollback em caso de erro
    await executeQuery('ROLLBACK');
    logger.error('Erro ao realizar carregamento de ATM:', error);
    return next(new AppError('Erro interno do servidor ao processar carregamento', 500, 'INTERNAL_ERROR'));
  }
}));

module.exports = router;
