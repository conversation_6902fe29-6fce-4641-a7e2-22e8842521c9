const express = require('express');
const { catchAsync, AppError } = require('../core/errorHandler');
const { executeQuery, executeTransaction } = require('../config/database');
const { authorize } = require('../auth/middleware');
const Joi = require('joi');
const { v4: uuidv4 } = require('uuid');
const logger = require('../core/logger');

const router = express.Router();

// Schemas de validação
const createCashRegisterSchema = Joi.object({
  register_number: Joi.string().required().max(10),
  description: Joi.string().allow('').max(500),
  branch_id: Joi.number().integer().positive().required(),
  status: Joi.string().valid('available', 'in_use', 'closed', 'maintenance').default('available')
});

const updateCashRegisterSchema = Joi.object({
  register_number: Joi.string().max(10),
  description: Joi.string().allow('').max(500),
  branch_id: Joi.number().integer().positive(),
  status: Joi.string().valid('available', 'in_use', 'closed', 'maintenance')
}).min(1);

/**
 * GET /api/cash-registers
 * Listar todos os caixas (Admin e Gerente)
 */
router.get('/', authorize('admin', 'gerente'), catchAsync(async (req, res) => {
  let query = `SELECT cr.id, cr.register_number, cr.description, cr.status, cr.created_at,
               b.name as branch_name, b.code as branch_code, b.id as branch_id
               FROM cash_registers cr
               JOIN branches b ON cr.branch_id = b.id`;

  let params = [];

  // Se não for admin, filtrar por balcão do utilizador
  if (req.user.role_name !== 'admin') {
    query += ' WHERE cr.branch_id = ?';
    params.push(req.user.branch_id);
  }

  query += ' ORDER BY cr.register_number';

  const cashRegisters = await executeQuery(query, params);

  res.status(200).json({
    status: 'success',
    message: 'Caixas obtidos com sucesso',
    data: {
      cash_registers: cashRegisters
    }
  });
}));

/**
 * GET /api/cash-registers/available
 * Listar caixas disponíveis para seleção
 */
router.get('/available', authorize('admin', 'gerente', 'caixa', 'tesoureiro'), catchAsync(async (req, res) => {
  // Obter caixas disponíveis
  let query = `SELECT cr.id, cr.register_number, cr.description, cr.status,
               b.name as branch_name, b.code as branch_code
               FROM cash_registers cr
               JOIN branches b ON cr.branch_id = b.id
               WHERE cr.status = 'available'`;

  let params = [];

  // Apenas Admin e Gerente visualizam todos os caixas
  // Tesoureiro e outros perfis visualizam apenas caixas da sua agência
  if (req.user.role_name !== 'admin' && req.user.role_name !== 'gerente') {
    query += ' AND cr.branch_id = ?';
    params.push(req.user.branch_id);
  }

  query += ' ORDER BY cr.register_number';

  const availableCashRegisters = await executeQuery(query, params);

  res.status(200).json({
    status: 'success',
    message: 'Caixas disponíveis obtidos com sucesso',
    data: {
      cash_registers: availableCashRegisters
    }
  });
}));

/**
 * POST /api/cash-registers
 * Criar novo caixa (apenas Admin e Gerente)
 */
router.post('/', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = createCashRegisterSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { register_number, description, branch_id, status } = value;

  // 2. Verificar se número de caixa já existe
  const existingCashRegisters = await executeQuery(
    'SELECT id FROM cash_registers WHERE register_number = ?',
    [register_number]
  );

  if (existingCashRegisters && existingCashRegisters.length > 0) {
    return next(new AppError('Número de caixa já está em uso', 409, 'REGISTER_NUMBER_EXISTS'));
  }

  // 3. Verificar se balcão existe
  const branches = await executeQuery('SELECT id, name FROM branches WHERE id = ?', [branch_id]);
  if (!branches || branches.length === 0) {
    return next(new AppError('Balcão não encontrado', 404, 'BRANCH_NOT_FOUND'));
  }

  // 4. Criar caixa
  const result = await executeQuery(
    'INSERT INTO cash_registers (register_number, description, branch_id, status) VALUES (?, ?, ?, ?)',
    [register_number, description || null, branch_id, status || 'available']
  );

  // 5. Buscar caixa criado com informações do balcão
  const newCashRegister = await executeQuery(
    `SELECT cr.id, cr.register_number, cr.description, cr.status, cr.created_at,
            b.name as branch_name, b.code as branch_code, b.id as branch_id
     FROM cash_registers cr
     JOIN branches b ON cr.branch_id = b.id
     WHERE cr.id = ?`,
    [result.insertId]
  );

  logger.info(`Caixa criado: ${register_number}`, {
    cashRegisterId: result.insertId,
    createdBy: req.user.id,
    branchId: branch_id
  });

  res.status(201).json({
    status: 'success',
    message: 'Caixa criado com sucesso',
    data: {
      cash_register: newCashRegister[0]
    }
  });
}));

/**
 * GET /api/cash-registers/:id
 * Obter caixa específico
 */
router.get('/:id', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const cashRegisters = await executeQuery(
    `SELECT cr.id, cr.register_number, cr.description, cr.status, cr.created_at,
            b.name as branch_name, b.code as branch_code, b.id as branch_id
     FROM cash_registers cr
     JOIN branches b ON cr.branch_id = b.id
     WHERE cr.id = ?`,
    [id]
  );

  if (!cashRegisters || cashRegisters.length === 0) {
    return next(new AppError('Caixa não encontrado', 404, 'CASH_REGISTER_NOT_FOUND'));
  }

  res.status(200).json({
    status: 'success',
    data: {
      cash_register: cashRegisters[0]
    }
  });
}));

/**
 * PUT /api/cash-registers/:id
 * Atualizar caixa (apenas Admin e Gerente)
 */
router.put('/:id', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // 1. Validar dados de entrada
  const { error, value } = updateCashRegisterSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  // 2. Verificar se caixa existe
  const existingCashRegisters = await executeQuery('SELECT id, register_number FROM cash_registers WHERE id = ?', [id]);
  if (!existingCashRegisters || existingCashRegisters.length === 0) {
    return next(new AppError('Caixa não encontrado', 404, 'CASH_REGISTER_NOT_FOUND'));
  }

  // 3. Se está alterando o número do caixa, verificar se já existe
  if (value.register_number && value.register_number !== existingCashRegisters[0].register_number) {
    const duplicateCheck = await executeQuery(
      'SELECT id FROM cash_registers WHERE register_number = ? AND id != ?',
      [value.register_number, id]
    );

    if (duplicateCheck && duplicateCheck.length > 0) {
      return next(new AppError('Número de caixa já está em uso', 409, 'REGISTER_NUMBER_EXISTS'));
    }
  }

  // 4. Se está alterando o balcão, verificar se existe
  if (value.branch_id) {
    const branches = await executeQuery('SELECT id FROM branches WHERE id = ?', [value.branch_id]);
    if (!branches || branches.length === 0) {
      return next(new AppError('Balcão não encontrado', 404, 'BRANCH_NOT_FOUND'));
    }
  }

  // 5. Construir query de atualização dinamicamente
  const updateFields = [];
  const updateValues = [];

  Object.entries(value).forEach(([key, val]) => {
    updateFields.push(`${key} = ?`);
    updateValues.push(val);
  });

  updateValues.push(id);

  await executeQuery(
    `UPDATE cash_registers SET ${updateFields.join(', ')} WHERE id = ?`,
    updateValues
  );

  // 6. Buscar caixa atualizado
  const updatedCashRegister = await executeQuery(
    `SELECT cr.id, cr.register_number, cr.description, cr.status, cr.created_at,
            b.name as branch_name, b.code as branch_code, b.id as branch_id
     FROM cash_registers cr
     JOIN branches b ON cr.branch_id = b.id
     WHERE cr.id = ?`,
    [id]
  );

  logger.info(`Caixa atualizado: ${updatedCashRegister[0].register_number}`, {
    cashRegisterId: id,
    updatedBy: req.user.id,
    changes: value
  });

  res.status(200).json({
    status: 'success',
    message: 'Caixa atualizado com sucesso',
    data: {
      cash_register: updatedCashRegister[0]
    }
  });
}));

/**
 * DELETE /api/cash-registers/:id
 * Remover caixa (apenas Admin)
 */
router.delete('/:id', authorize('admin'), catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // 1. Verificar se caixa existe
  const existingCashRegisters = await executeQuery('SELECT id, register_number FROM cash_registers WHERE id = ?', [id]);
  if (!existingCashRegisters || existingCashRegisters.length === 0) {
    return next(new AppError('Caixa não encontrado', 404, 'CASH_REGISTER_NOT_FOUND'));
  }

  // 2. Verificar se há sessões associadas ao caixa
  const associatedSessions = await executeQuery('SELECT COUNT(*) as count FROM cash_register_sessions WHERE cash_register_id = ?', [id]);
  if (associatedSessions[0].count > 0) {
    return next(new AppError('Não é possível remover caixa com sessões associadas', 409, 'CASH_REGISTER_HAS_SESSIONS'));
  }

  // 3. Remover caixa
  await executeQuery('DELETE FROM cash_registers WHERE id = ?', [id]);

  logger.info(`Caixa removido: ${existingCashRegisters[0].register_number}`, {
    cashRegisterId: id,
    deletedBy: req.user.id,
    registerNumber: existingCashRegisters[0].register_number
  });

  res.status(200).json({
    status: 'success',
    message: 'Caixa removido com sucesso'
  });
}));

/**
 * POST /api/cash-registers/sessions/open
 * Abrir sessão de caixa
 */
router.post('/sessions/open', authorize('caixa'), catchAsync(async (req, res, next) => {
  const { error, value } = Joi.object({
    cash_register_number: Joi.string().required(),
    opening_balance: Joi.number().min(0).required(),
    denominations: Joi.object({
      notes_10000: Joi.number().integer().min(0).default(0),
      notes_5000: Joi.number().integer().min(0).default(0),
      notes_2000: Joi.number().integer().min(0).default(0),
      notes_1000: Joi.number().integer().min(0).default(0),
      notes_500: Joi.number().integer().min(0).default(0),
      notes_200: Joi.number().integer().min(0).default(0),
      notes_100: Joi.number().integer().min(0).default(0),
      notes_50: Joi.number().integer().min(0).default(0),
      coins_10: Joi.number().integer().min(0).default(0),
      coins_5: Joi.number().integer().min(0).default(0),
      coins_1: Joi.number().integer().min(0).default(0)
    }).required(),
    notes: Joi.string().allow('').max(500)
  }).validate(req.body);

  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { cash_register_number, opening_balance, denominations, notes } = value;

  // 1. Verificar se usuário já tem sessão ativa
  const activeSessions = await executeQuery(
    'SELECT id FROM cash_register_sessions WHERE user_id = ? AND status = "open"',
    [req.user.id]
  );

  if (activeSessions && activeSessions.length > 0) {
    return next(new AppError('Usuário já possui uma sessão de caixa ativa', 409, 'ACTIVE_SESSION_EXISTS'));
  }

  // 2. Buscar caixa pelo número
  const cashRegisters = await executeQuery(
    'SELECT id, register_number, status FROM cash_registers WHERE register_number = ?',
    [cash_register_number]
  );

  if (!cashRegisters || cashRegisters.length === 0) {
    return next(new AppError('Caixa não encontrado', 404, 'CASH_REGISTER_NOT_FOUND'));
  }

  const cashRegister = cashRegisters[0];

  if (cashRegister.status !== 'available') {
    return next(new AppError('Caixa não está disponível', 400, 'CASH_REGISTER_NOT_AVAILABLE'));
  }

  // 3. Calcular total das denominações
  const calculatedTotal = (
    denominations.notes_10000 * 10000 +
    denominations.notes_5000 * 5000 +
    denominations.notes_2000 * 2000 +
    denominations.notes_1000 * 1000 +
    denominations.notes_500 * 500 +
    denominations.notes_200 * 200 +
    denominations.notes_100 * 100 +
    denominations.notes_50 * 50 +
    denominations.coins_10 * 10 +
    denominations.coins_5 * 5 +
    denominations.coins_1 * 1
  );

  // 4. Verificar se total das denominações confere
  if (Math.abs(calculatedTotal - opening_balance) > 0.01) {
    return next(new AppError('Total das denominações não confere com o saldo inicial', 400, 'DENOMINATION_MISMATCH'));
  }

  // 5. Criar sessão de caixa
  const sessionId = uuidv4();

  await executeTransaction(async (connection) => {
    // Criar sessão
    await connection.query(
      `INSERT INTO cash_register_sessions
       (id, user_id, cash_register_id, opening_balance, status, notes, opened_at)
       VALUES (?, ?, ?, ?, 'open', ?, NOW())`,
      [sessionId, req.user.id, cashRegister.id, opening_balance, notes || null]
    );

    // Salvar denominações de abertura
    for (const [key, quantity] of Object.entries(denominations)) {
      if (quantity > 0) {
        const denominationValue = key.includes('notes_') ?
          parseInt(key.replace('notes_', '')) :
          parseInt(key.replace('coins_', ''));

        const denominationType = key.includes('notes_') ? 'note' : 'coin';

        await connection.query(
          `INSERT INTO cash_denominations
           (session_id, denomination_type, denomination_value, quantity, total_value, operation_type)
           VALUES (?, ?, ?, ?, ?, 'opening')`,
          [sessionId, denominationType, denominationValue, quantity, quantity * denominationValue]
        );
      }
    }

    // Atualizar status do caixa para 'in_use'
    await connection.query(
      'UPDATE cash_registers SET status = "in_use" WHERE id = ?',
      [cashRegister.id]
    );
  });

  // 6. Buscar sessão criada com informações completas
  const session = await executeQuery(
    `SELECT s.id, s.user_id, s.opening_balance, s.status, s.opened_at, s.notes,
            cr.id as cash_register_id, cr.register_number as cash_register_number,
            u.full_name as user_name
     FROM cash_register_sessions s
     JOIN cash_registers cr ON s.cash_register_id = cr.id
     JOIN users u ON s.user_id = u.id
     WHERE s.id = ?`,
    [sessionId]
  );

  logger.info(`Sessão de caixa aberta: ${cash_register_number}`, {
    sessionId,
    userId: req.user.id,
    cashRegisterId: cashRegister.id,
    openingBalance: opening_balance
  });

  res.status(201).json({
    status: 'success',
    message: 'Sessão de caixa aberta com sucesso',
    data: {
      session: {
        ...session[0],
        current_balance: opening_balance,
        denominations
      }
    }
  });
}));

/**
 * POST /api/cash-registers/sessions/close
 * Fechar sessão de caixa
 */
router.post('/sessions/close', authorize('caixa'), catchAsync(async (req, res, next) => {
  const { error, value } = Joi.object({
    session_id: Joi.string().uuid().required(),
    closing_balance: Joi.number().min(0).required(),
    denominations: Joi.object({
      notes_10000: Joi.number().integer().min(0).default(0),
      notes_5000: Joi.number().integer().min(0).default(0),
      notes_2000: Joi.number().integer().min(0).default(0),
      notes_1000: Joi.number().integer().min(0).default(0),
      notes_500: Joi.number().integer().min(0).default(0),
      notes_200: Joi.number().integer().min(0).default(0),
      notes_100: Joi.number().integer().min(0).default(0),
      notes_50: Joi.number().integer().min(0).default(0),
      coins_10: Joi.number().integer().min(0).default(0),
      coins_5: Joi.number().integer().min(0).default(0),
      coins_1: Joi.number().integer().min(0).default(0)
    }).required(),
    notes: Joi.string().allow('').max(500)
  }).validate(req.body);

  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { session_id, closing_balance, denominations, notes } = value;

  // 1. Verificar se sessão existe e pertence ao usuário
  const sessions = await executeQuery(
    `SELECT s.id, s.user_id, s.opening_balance, s.status, s.cash_register_id,
            cr.register_number as cash_register_number
     FROM cash_register_sessions s
     JOIN cash_registers cr ON s.cash_register_id = cr.id
     WHERE s.id = ? AND s.user_id = ?`,
    [session_id, req.user.id]
  );

  if (!sessions || sessions.length === 0) {
    return next(new AppError('Sessão não encontrada ou não pertence ao usuário', 404, 'SESSION_NOT_FOUND'));
  }

  const session = sessions[0];

  if (session.status !== 'open') {
    return next(new AppError('Sessão não está aberta', 400, 'SESSION_NOT_OPEN'));
  }

  // 2. Calcular total das denominações
  const calculatedTotal = (
    denominations.notes_10000 * 10000 +
    denominations.notes_5000 * 5000 +
    denominations.notes_2000 * 2000 +
    denominations.notes_1000 * 1000 +
    denominations.notes_500 * 500 +
    denominations.notes_200 * 200 +
    denominations.notes_100 * 100 +
    denominations.notes_50 * 50 +
    denominations.coins_10 * 10 +
    denominations.coins_5 * 5 +
    denominations.coins_1 * 1
  );

  // 3. Verificar se total das denominações confere
  if (Math.abs(calculatedTotal - closing_balance) > 0.01) {
    return next(new AppError('Total das denominações não confere com o saldo de fechamento', 400, 'DENOMINATION_MISMATCH'));
  }

  // 4. Calcular diferença (pode ser positiva ou negativa)
  const difference = closing_balance - session.opening_balance;

  // 5. Fechar sessão
  await executeTransaction(async (connection) => {
    // Atualizar sessão
    await connection.query(
      `UPDATE cash_register_sessions
       SET closing_balance = ?, difference = ?, status = 'closed', notes = ?, closed_at = NOW()
       WHERE id = ?`,
      [closing_balance, difference, notes || null, session_id]
    );

    // Salvar denominações de fechamento
    for (const [key, quantity] of Object.entries(denominations)) {
      if (quantity > 0) {
        const denominationValue = key.includes('notes_') ?
          parseInt(key.replace('notes_', '')) :
          parseInt(key.replace('coins_', ''));

        const denominationType = key.includes('notes_') ? 'note' : 'coin';

        await connection.query(
          `INSERT INTO cash_denominations
           (session_id, denomination_type, denomination_value, quantity, total_value, operation_type)
           VALUES (?, ?, ?, ?, ?, 'closing')`,
          [session_id, denominationType, denominationValue, quantity, quantity * denominationValue]
        );
      }
    }

    // Liberar caixa (voltar para 'available')
    await connection.query(
      'UPDATE cash_registers SET status = "available" WHERE id = ?',
      [session.cash_register_id]
    );
  });

  // 6. Buscar sessão fechada com informações completas
  const closedSession = await executeQuery(
    `SELECT s.id, s.user_id, s.opening_balance, s.closing_balance, s.difference,
            s.status, s.opened_at, s.closed_at, s.notes,
            cr.id as cash_register_id, cr.register_number as cash_register_number,
            u.full_name as user_name
     FROM cash_register_sessions s
     JOIN cash_registers cr ON s.cash_register_id = cr.id
     JOIN users u ON s.user_id = u.id
     WHERE s.id = ?`,
    [session_id]
  );

  logger.info(`Sessão de caixa fechada: ${session.cash_register_number}`, {
    sessionId: session_id,
    userId: req.user.id,
    openingBalance: session.opening_balance,
    closingBalance: closing_balance,
    difference
  });

  res.status(200).json({
    status: 'success',
    message: 'Sessão de caixa fechada com sucesso',
    data: {
      session: {
        ...closedSession[0],
        closing_denominations: denominations
      }
    }
  });
}));

/**
 * GET /api/cash-registers/sessions/current
 * Obter sessão ativa do usuário
 */
router.get('/sessions/current', authorize('caixa'), catchAsync(async (req, res, next) => {
  // Buscar sessão ativa do usuário
  const sessions = await executeQuery(
    `SELECT s.id, s.user_id, s.opening_balance, s.status, s.opened_at, s.notes,
            cr.id as cash_register_id, cr.register_number as cash_register_number,
            cr.description as cash_register_description,
            u.full_name as user_name,
            b.name as branch_name
     FROM cash_register_sessions s
     JOIN cash_registers cr ON s.cash_register_id = cr.id
     JOIN users u ON s.user_id = u.id
     LEFT JOIN branches b ON cr.branch_id = b.id
     WHERE s.user_id = ? AND s.status = 'open'
     ORDER BY s.opened_at DESC
     LIMIT 1`,
    [req.user.id]
  );

  if (!sessions || sessions.length === 0) {
    return res.status(200).json({
      status: 'success',
      message: 'Nenhuma sessão ativa encontrada',
      data: {
        session: null
      }
    });
  }

  const session = sessions[0];

  // Buscar denominações de abertura
  const openingDenominations = await executeQuery(
    `SELECT denomination_type, denomination_value, quantity, total_value
     FROM cash_denominations
     WHERE session_id = ? AND operation_type = 'opening'`,
    [session.id]
  );

  // Converter denominações para formato esperado
  const denominations = {
    notes_10000: 0, notes_5000: 0, notes_2000: 0, notes_1000: 0, notes_500: 0,
    notes_200: 0, notes_100: 0, notes_50: 0, coins_10: 0, coins_5: 0, coins_1: 0
  };

  openingDenominations.forEach(denom => {
    const key = `${denom.denomination_type}s_${denom.denomination_value}`;
    if (denominations.hasOwnProperty(key)) {
      denominations[key] = denom.quantity;
    }
  });

  res.status(200).json({
    status: 'success',
    message: 'Sessão ativa encontrada',
    data: {
      session: {
        ...session,
        current_balance: session.opening_balance, // TODO: Calcular saldo atual com operações
        opening_denominations: denominations
      }
    }
  });
}));

/**
 * GET /api/cash-registers/sessions
 * Listar sessões de caixa
 */
router.get('/sessions', authorize('admin', 'gerente', 'tesoureiro'), (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Listagem de sessões - Em desenvolvimento',
    data: []
  });
});

/**
 * POST /api/cash-registers/operations/deposit
 * Realizar depósito em conta
 */
router.post('/operations/deposit', authorize('caixa'), catchAsync(async (req, res, next) => {
  const { error, value } = Joi.object({
    account_number: Joi.string().required(),
    amount: Joi.number().positive().required(),
    denominations: Joi.object({
      notes_10000: Joi.number().integer().min(0).default(0),
      notes_5000: Joi.number().integer().min(0).default(0),
      notes_2000: Joi.number().integer().min(0).default(0),
      notes_1000: Joi.number().integer().min(0).default(0),
      notes_500: Joi.number().integer().min(0).default(0),
      notes_200: Joi.number().integer().min(0).default(0),
      notes_100: Joi.number().integer().min(0).default(0),
      notes_50: Joi.number().integer().min(0).default(0),
      coins_10: Joi.number().integer().min(0).default(0),
      coins_5: Joi.number().integer().min(0).default(0),
      coins_1: Joi.number().integer().min(0).default(0)
    }).required(),
    description: Joi.string().max(500).default('Depósito em dinheiro'),
    reference_number: Joi.string().max(50).optional()
  }).validate(req.body);

  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { account_number, amount, denominations, description, reference_number } = value;

  // 1. Verificar se usuário tem sessão ativa
  const activeSessions = await executeQuery(
    `SELECT s.id, s.cash_register_id, cr.register_number
     FROM cash_register_sessions s
     JOIN cash_registers cr ON s.cash_register_id = cr.id
     WHERE s.user_id = ? AND s.status = 'open'`,
    [req.user.id]
  );

  if (!activeSessions || activeSessions.length === 0) {
    return next(new AppError('Usuário não possui sessão de caixa ativa', 403, 'NO_ACTIVE_SESSION'));
  }

  const session = activeSessions[0];

  // 2. Verificar se conta existe
  const accounts = await executeQuery(
    'SELECT id, account_number, balance, status FROM accounts WHERE account_number = ?',
    [account_number]
  );

  if (!accounts || accounts.length === 0) {
    return next(new AppError('Conta não encontrada', 404, 'ACCOUNT_NOT_FOUND'));
  }

  const account = accounts[0];

  if (account.status !== 'active') {
    return next(new AppError('Conta não está ativa', 400, 'ACCOUNT_INACTIVE'));
  }

  // 3. Calcular total das denominações
  const calculatedTotal = (
    denominations.notes_10000 * 10000 +
    denominations.notes_5000 * 5000 +
    denominations.notes_2000 * 2000 +
    denominations.notes_1000 * 1000 +
    denominations.notes_500 * 500 +
    denominations.notes_200 * 200 +
    denominations.notes_100 * 100 +
    denominations.notes_50 * 50 +
    denominations.coins_10 * 10 +
    denominations.coins_5 * 5 +
    denominations.coins_1 * 1
  );

  // 4. Verificar se total das denominações confere
  if (Math.abs(calculatedTotal - amount) > 0.01) {
    return next(new AppError('Total das denominações não confere com o valor do depósito', 400, 'DENOMINATION_MISMATCH'));
  }

  // 5. Gerar código de transação único
  const transactionCode = `DEP${Date.now()}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;

  await executeTransaction(async (connection) => {
    // Criar transação
    const transactionId = uuidv4();
    await connection.query(
      `INSERT INTO transactions
       (id, transaction_code, transaction_type, amount, currency_id, destination_account_id,
        description, reference_number, status, processed_by, session_id, processed_at)
       VALUES (?, ?, 'deposit', ?, 1, ?, ?, ?, 'completed', ?, ?, NOW())`,
      [transactionId, transactionCode, amount, account.id, description, reference_number, req.user.id, session.id]
    );

    // Atualizar saldo da conta
    await connection.query(
      'UPDATE accounts SET balance = balance + ? WHERE id = ?',
      [amount, account.id]
    );

    // Salvar denominações da transação
    for (const [key, quantity] of Object.entries(denominations)) {
      if (quantity > 0) {
        const denominationValue = key.includes('notes_') ?
          parseInt(key.replace('notes_', '')) :
          parseInt(key.replace('coins_', ''));

        const denominationType = key.includes('notes_') ? 'note' : 'coin';

        await connection.query(
          `INSERT INTO transaction_denominations
           (transaction_id, denomination_type, denomination_value, quantity, total_value)
           VALUES (?, ?, ?, ?, ?)`,
          [transactionId, denominationType, denominationValue, quantity, quantity * denominationValue]
        );
      }
    }

    // Log da operação
    logger.info(`Depósito realizado: ${amount} AOA na conta ${account_number}`, {
      transactionId,
      transactionCode,
      accountId: account.id,
      amount,
      processedBy: req.user.id,
      sessionId: session.id
    });

    res.status(201).json({
      status: 'success',
      message: 'Depósito realizado com sucesso',
      data: {
        transaction: {
          id: transactionId,
          transaction_code: transactionCode,
          type: 'deposit',
          amount,
          account_number,
          description,
          reference_number,
          processed_at: new Date().toISOString(),
          processed_by: req.user.full_name,
          cash_register: session.register_number
        }
      }
    });
  });
}));

/**
 * POST /api/cash-registers/operations/withdrawal
 * Realizar Levantamento de conta
 */
router.post('/operations/withdrawal', authorize('caixa'), catchAsync(async (req, res, next) => {
  const { error, value } = Joi.object({
    account_number: Joi.string().required(),
    amount: Joi.number().positive().required(),
    denominations: Joi.object({
      notes_10000: Joi.number().integer().min(0).default(0),
      notes_5000: Joi.number().integer().min(0).default(0),
      notes_2000: Joi.number().integer().min(0).default(0),
      notes_1000: Joi.number().integer().min(0).default(0),
      notes_500: Joi.number().integer().min(0).default(0),
      notes_200: Joi.number().integer().min(0).default(0),
      notes_100: Joi.number().integer().min(0).default(0),
      notes_50: Joi.number().integer().min(0).default(0),
      coins_10: Joi.number().integer().min(0).default(0),
      coins_5: Joi.number().integer().min(0).default(0),
      coins_1: Joi.number().integer().min(0).default(0)
    }).required(),
    description: Joi.string().max(500).default('Levantamento em dinheiro'),
    reference_number: Joi.string().max(50).optional()
  }).validate(req.body);

  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { account_number, amount, denominations, description, reference_number } = value;

  // 1. Verificar se usuário tem sessão ativa
  const activeSessions = await executeQuery(
    `SELECT s.id, s.cash_register_id, cr.register_number
     FROM cash_register_sessions s
     JOIN cash_registers cr ON s.cash_register_id = cr.id
     WHERE s.user_id = ? AND s.status = 'open'`,
    [req.user.id]
  );

  if (!activeSessions || activeSessions.length === 0) {
    return next(new AppError('Usuário não possui sessão de caixa ativa', 403, 'NO_ACTIVE_SESSION'));
  }

  const session = activeSessions[0];

  // 2. Verificar se conta existe e tem saldo suficiente
  const accounts = await executeQuery(
    'SELECT id, account_number, balance, status FROM accounts WHERE account_number = ?',
    [account_number]
  );

  if (!accounts || accounts.length === 0) {
    return next(new AppError('Conta não encontrada', 404, 'ACCOUNT_NOT_FOUND'));
  }

  const account = accounts[0];

  if (account.status !== 'active') {
    return next(new AppError('Conta não está ativa', 400, 'ACCOUNT_INACTIVE'));
  }

  if (parseFloat(account.balance) < amount) {
    return next(new AppError('Saldo insuficiente na conta', 400, 'INSUFFICIENT_BALANCE'));
  }

  // 3. Calcular total das denominações
  const calculatedTotal = (
    denominations.notes_10000 * 10000 +
    denominations.notes_5000 * 5000 +
    denominations.notes_2000 * 2000 +
    denominations.notes_1000 * 1000 +
    denominations.notes_500 * 500 +
    denominations.notes_200 * 200 +
    denominations.notes_100 * 100 +
    denominations.notes_50 * 50 +
    denominations.coins_10 * 10 +
    denominations.coins_5 * 5 +
    denominations.coins_1 * 1
  );

  // 4. Verificar se total das denominações confere
  if (Math.abs(calculatedTotal - amount) > 0.01) {
    return next(new AppError('Total das denominações não confere com o valor do Levantamento', 400, 'DENOMINATION_MISMATCH'));
  }

  // 5. Gerar código de transação único
  const transactionCode = `WTH${Date.now()}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;

  await executeTransaction(async (connection) => {
    // Criar transação
    const transactionId = uuidv4();
    await connection.query(
      `INSERT INTO transactions
       (id, transaction_code, transaction_type, amount, currency_id, source_account_id,
        description, reference_number, status, processed_by, session_id, processed_at)
       VALUES (?, ?, 'withdrawal', ?, 1, ?, ?, ?, 'completed', ?, ?, NOW())`,
      [transactionId, transactionCode, amount, account.id, description, reference_number, req.user.id, session.id]
    );

    // Atualizar saldo da conta
    await connection.query(
      'UPDATE accounts SET balance = balance - ? WHERE id = ?',
      [amount, account.id]
    );

    // Salvar denominações da transação
    for (const [key, quantity] of Object.entries(denominations)) {
      if (quantity > 0) {
        const denominationValue = key.includes('notes_') ?
          parseInt(key.replace('notes_', '')) :
          parseInt(key.replace('coins_', ''));

        const denominationType = key.includes('notes_') ? 'note' : 'coin';

        await connection.query(
          `INSERT INTO transaction_denominations
           (transaction_id, denomination_type, denomination_value, quantity, total_value)
           VALUES (?, ?, ?, ?, ?)`,
          [transactionId, denominationType, denominationValue, quantity, quantity * denominationValue]
        );
      }
    }

    // Log da operação
    logger.info(`Levantamento realizado: ${amount} AOA da conta ${account_number}`, {
      transactionId,
      transactionCode,
      accountId: account.id,
      amount,
      processedBy: req.user.id,
      sessionId: session.id
    });

    res.status(201).json({
      status: 'success',
      message: 'Levantamento realizado com sucesso',
      data: {
        transaction: {
          id: transactionId,
          transaction_code: transactionCode,
          type: 'withdrawal',
          amount,
          account_number,
          description,
          reference_number,
          processed_at: new Date().toISOString(),
          processed_by: req.user.full_name,
          cash_register: session.register_number
        }
      }
    });
  });
}));

/**
 * GET /api/cash-registers/operations
 * Listar operações de caixa da sessão atual
 */
router.get('/operations', authorize('caixa'), catchAsync(async (req, res, next) => {
  // 1. Verificar se usuário tem sessão ativa
  const activeSessions = await executeQuery(
    `SELECT s.id, s.cash_register_id, cr.register_number, s.opened_at
     FROM cash_register_sessions s
     JOIN cash_registers cr ON s.cash_register_id = cr.id
     WHERE s.user_id = ? AND s.status = 'open'`,
    [req.user.id]
  );

  if (!activeSessions || activeSessions.length === 0) {
    return next(new AppError('Usuário não possui sessão de caixa ativa', 403, 'NO_ACTIVE_SESSION'));
  }

  const session = activeSessions[0];

  // 2. Buscar operações da sessão
  const operations = await executeQuery(
    `SELECT t.id, t.transaction_code, t.transaction_type, t.amount, t.description,
            t.reference_number, t.processed_at, t.status,
            sa.account_number as source_account_number,
            da.account_number as destination_account_number,
            sc.full_name as source_client_name,
            dc.full_name as destination_client_name
     FROM transactions t
     LEFT JOIN accounts sa ON t.source_account_id = sa.id
     LEFT JOIN accounts da ON t.destination_account_id = da.id
     LEFT JOIN clients sc ON sa.client_id = sc.id
     LEFT JOIN clients dc ON da.client_id = dc.id
     WHERE t.session_id = ?
     ORDER BY t.processed_at DESC
     LIMIT 50`,
    [session.id]
  );

  // 3. Calcular totais
  const totals = await executeQuery(
    `SELECT
       SUM(CASE WHEN transaction_type = 'deposit' THEN amount ELSE 0 END) as total_deposits,
       SUM(CASE WHEN transaction_type = 'withdrawal' THEN amount ELSE 0 END) as total_withdrawals,
       COUNT(CASE WHEN transaction_type = 'deposit' THEN 1 END) as count_deposits,
       COUNT(CASE WHEN transaction_type = 'withdrawal' THEN 1 END) as count_withdrawals
     FROM transactions
     WHERE session_id = ? AND status = 'completed'`,
    [session.id]
  );

  const sessionTotals = totals[0] || {
    total_deposits: 0,
    total_withdrawals: 0,
    count_deposits: 0,
    count_withdrawals: 0
  };

  res.status(200).json({
    status: 'success',
    message: 'Operações de caixa listadas com sucesso',
    data: {
      session: {
        id: session.id,
        cash_register: session.register_number,
        opened_at: session.opened_at
      },
      totals: {
        deposits: {
          amount: parseFloat(sessionTotals.total_deposits) || 0,
          count: parseInt(sessionTotals.count_deposits) || 0
        },
        withdrawals: {
          amount: parseFloat(sessionTotals.total_withdrawals) || 0,
          count: parseInt(sessionTotals.count_withdrawals) || 0
        },
        net_amount: (parseFloat(sessionTotals.total_deposits) || 0) - (parseFloat(sessionTotals.total_withdrawals) || 0)
      },
      operations: operations.map(op => ({
        id: op.id,
        transaction_code: op.transaction_code,
        type: op.transaction_type,
        amount: parseFloat(op.amount),
        description: op.description,
        reference_number: op.reference_number,
        processed_at: op.processed_at,
        status: op.status,
        account_number: op.source_account_number || op.destination_account_number,
        client_name: op.source_client_name || op.destination_client_name
      }))
    }
  });
}));

/**
 * POST /api/cash-registers/:id/force-close
 * Forçar fecho de caixa (apenas Admin e Gerente)
 */
router.post('/:id/force-close', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // 1. Verificar se caixa existe
  const cashRegisters = await executeQuery(
    `SELECT cr.id, cr.register_number, cr.status, cr.branch_id,
            b.name as branch_name
     FROM cash_registers cr
     JOIN branches b ON cr.branch_id = b.id
     WHERE cr.id = ?`,
    [id]
  );

  if (!cashRegisters || cashRegisters.length === 0) {
    return next(new AppError('Caixa não encontrado', 404, 'CASH_REGISTER_NOT_FOUND'));
  }

  const cashRegister = cashRegisters[0];

  if (cashRegister.status !== 'in_use') {
    return next(new AppError('Caixa não está em uso', 400, 'CASH_REGISTER_NOT_IN_USE'));
  }

  // 2. Buscar sessão ativa do caixa
  const activeSessions = await executeQuery(
    `SELECT s.id, s.user_id, s.opening_balance, s.opened_at,
            u.full_name as user_name, u.email as user_email
     FROM cash_register_sessions s
     JOIN users u ON s.user_id = u.id
     WHERE s.cash_register_id = ? AND s.status = 'open'`,
    [id]
  );

  if (!activeSessions || activeSessions.length === 0) {
    return next(new AppError('Nenhuma sessão ativa encontrada para este caixa', 404, 'NO_ACTIVE_SESSION'));
  }

  const session = activeSessions[0];

  // 3. Calcular saldo atual baseado nas operações
  const operationTotals = await executeQuery(
    `SELECT
       SUM(CASE WHEN transaction_type = 'deposit' THEN amount ELSE 0 END) as total_deposits,
       SUM(CASE WHEN transaction_type = 'withdrawal' THEN amount ELSE 0 END) as total_withdrawals
     FROM transactions
     WHERE session_id = ? AND status = 'completed'`,
    [session.id]
  );

  const totalDeposits = parseFloat(operationTotals[0]?.total_deposits) || 0;
  const totalWithdrawals = parseFloat(operationTotals[0]?.total_withdrawals) || 0;
  const currentBalance = session.opening_balance + totalDeposits - totalWithdrawals;

  await executeTransaction([
    // 4. Fechar sessão forçadamente
    {
      query: `UPDATE cash_register_sessions
              SET closing_balance_declared = ?, closing_balance_validated = ?, status = 'force_closed',
                  notes = CONCAT(COALESCE(notes, ''), ' [FECHAMENTO FORÇADO POR: ', ?, ']'),
                  closed_at = NOW(), validated_at = NOW(), validated_by = ?
              WHERE id = ?`,
      params: [currentBalance, currentBalance, req.user.full_name, req.user.id, session.id]
    },

    // 5. Liberar caixa (voltar para 'available')
    {
      query: 'UPDATE cash_registers SET status = ? WHERE id = ?',
      params: ['available', id]
    },

    // 6. Criar entrada de auditoria
    {
      query: `INSERT INTO audit_logs
              (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      params: [
        req.user.id,
        'force_close_cash_register',
        'cash_register_sessions',
        session.id,
        JSON.stringify({
          status: 'open',
          cash_register_id: id,
          register_number: cashRegister.register_number,
          operator: session.user_name,
          opening_balance: session.opening_balance
        }),
        JSON.stringify({
          status: 'force_closed',
          closing_balance: currentBalance,
          difference: currentBalance - session.opening_balance,
          forced_by: req.user.full_name,
          forced_at: new Date().toISOString()
        }),
        req.ip || req.connection?.remoteAddress || 'unknown',
        req.get('User-Agent') || 'unknown'
      ]
    }
  ]);

  // 7. Se há saldo positivo, registrar no log para transferência à tesouraria
  if (currentBalance > 0) {
    logger.info(`Saldo de ${currentBalance} AOA deve ser transferido para tesouraria`, {
      cashRegisterId: id,
      sessionId: session.id,
      amount: currentBalance,
      forcedBy: req.user.id
    });
  }

  logger.security(`Fechamento forçado de caixa: ${cashRegister.register_number}`, {
    cashRegisterId: id,
    sessionId: session.id,
    registerNumber: cashRegister.register_number,
    operator: session.user_name,
    operatorEmail: session.user_email,
    openingBalance: session.opening_balance,
    closingBalance: currentBalance,
    difference: currentBalance - session.opening_balance,
    forcedBy: req.user.id,
    forcedByName: req.user.full_name,
    branchName: cashRegister.branch_name,
    ip: req.ip
  });

  res.status(200).json({
    status: 'success',
    message: 'Caixa fechado forçadamente com sucesso',
    data: {
      cash_register: {
        id: cashRegister.id,
        register_number: cashRegister.register_number,
        status: 'available',
        branch_name: cashRegister.branch_name
      },
      session: {
        id: session.id,
        operator: session.user_name,
        opening_balance: session.opening_balance,
        closing_balance: currentBalance,
        difference: currentBalance - session.opening_balance,
        forced_by: req.user.full_name,
        forced_at: new Date().toISOString()
      }
    }
  });
}));

module.exports = router;
