const express = require('express');
const Joi = require('joi');
const { v4: uuidv4 } = require('uuid');
const { authorize } = require('../auth/middleware');
const { executeQuery } = require('../config/database');
const { catchAsync } = require('../core/errorHandler');
const { AppError } = require('../core/errorHandler');
const logger = require('../core/logger');
const vaultService = require('../services/vaultService');

const router = express.Router();

// Schema de validação para saldo inicial
const initialBalanceSchema = Joi.object({
  amount: Joi.number().min(0).max(999999999.99).required(),
  notes: Joi.string().min(10).max(500).optional().allow('')
});

/**
 * GET /api/system/vault/balance
 * Obter saldo atual do cofre principal
 */
router.get('/vault/balance', authorize('admin'), catchAsync(async (req, res, next) => {
  try {
    // Buscar saldo atual do cofre nas configurações do sistema
    const balanceSettings = await executeQuery(
      'SELECT setting_value FROM system_settings WHERE setting_key = ?',
      ['vault_initial_balance']
    );

    const currentBalance = balanceSettings.length > 0 
      ? parseFloat(balanceSettings[0].setting_value) || 0 
      : 0;

    res.status(200).json({
      status: 'success',
      data: {
        balance: currentBalance,
        currency: 'AOA',
        last_updated: balanceSettings.length > 0 ? balanceSettings[0].updated_at : null
      }
    });

  } catch (error) {
    logger.error('Erro ao obter saldo do cofre:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * POST /api/system/vault/initial-balance
 * Definir saldo inicial do cofre principal (apenas Admin)
 */
router.post('/vault/initial-balance', authorize('admin'), catchAsync(async (req, res, next) => {
  try {
    // Validar dados de entrada
    const { error, value } = initialBalanceSchema.validate(req.body);
    if (error) {
      return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
    }

    const { amount, notes } = value;
    const userId = req.user.id;
    const userName = req.user.name;

    // Verificar se já existe uma configuração de saldo inicial
    const existingBalance = await executeQuery(
      'SELECT * FROM system_settings WHERE setting_key = ?',
      ['vault_initial_balance']
    );

    let query, params;
    if (existingBalance.length > 0) {
      // Atualizar saldo existente
      query = `
        UPDATE system_settings 
        SET setting_value = ?, updated_at = NOW(), updated_by = ?
        WHERE setting_key = ?
      `;
      params = [amount.toString(), userId, 'vault_initial_balance'];
    } else {
      // Criar nova configuração de saldo
      query = `
        INSERT INTO system_settings (setting_key, setting_value, updated_by, updated_at)
        VALUES (?, ?, ?, NOW())
      `;
      params = ['vault_initial_balance', amount.toString(), userId];
    }

    await executeQuery(query, params);

    // Registar na auditoria
    await executeQuery(`
      INSERT INTO audit_logs (
        user_id, action, table_name, record_id,
        old_values, new_values, ip_address, user_agent, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      userId,
      existingBalance.length > 0 ? 'UPDATE' : 'CREATE',
      'system_settings',
      'vault_initial_balance',
      existingBalance.length > 0 ? JSON.stringify({ balance: existingBalance[0].setting_value }) : null,
      JSON.stringify({ balance: amount, notes: notes || null }),
      req.ip,
      req.get('User-Agent')
    ]);

    // TODO: Registar no log de segurança (tabela security_logs não existe)
    // const securityLogId = uuidv4();
    // await executeQuery(`
    //   INSERT INTO security_logs (
    //     id, user_id, action, resource, details,
    //     ip_address, user_agent, created_at
    //   ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    // `, [
    //   securityLogId,
    //   userId,
    //   'VAULT_BALANCE_SET',
    //   'system_settings',
    //   JSON.stringify({
    //     action: 'set_initial_balance',
    //     amount: amount,
    //     notes: notes || null,
    //     user: userName
    //   }),
    //   req.ip,
    //   req.get('User-Agent')
    // ]);

    logger.info(`Saldo inicial do cofre definido: ${amount} AOA por ${userName} (ID: ${userId})`);

    res.status(200).json({
      status: 'success',
      message: 'Saldo inicial definido com sucesso',
      data: {
        balance: amount,
        currency: 'AOA',
        notes: notes || null,
        set_by: userName,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Erro ao definir saldo inicial:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * GET /api/system/settings
 * Obter configurações do sistema (Admin, Gerente)
 */
router.get('/settings', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  try {
    const settings = await executeQuery(
      'SELECT setting_key, setting_value, updated_at FROM system_settings ORDER BY setting_key'
    );

    const settingsMap = {};
    settings.forEach(setting => {
      settingsMap[setting.setting_key] = {
        value: setting.setting_value,
        updated_at: setting.updated_at
      };
    });

    res.status(200).json({
      status: 'success',
      data: { settings: settingsMap }
    });

  } catch (error) {
    logger.error('Erro ao obter configurações do sistema:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * GET /api/system/vaults
 * Obter todos os cofres principais (Admin, Gerente, Tesoureiro)
 */
router.get('/vaults', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  try {
    const { branch_id, is_active } = req.query;
    
    const filters = {};
    if (branch_id) filters.branch_id = branch_id;
    if (is_active !== undefined) filters.is_active = is_active === 'true';

    const vaults = await vaultService.getAllVaults(filters);

    res.status(200).json({
      status: 'success',
      data: {
        vaults,
        total: vaults.length
      }
    });

  } catch (error) {
    logger.error('Erro ao obter cofres principais:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * GET /api/system/vaults/:id
 * Obter cofre específico por ID (Admin, Gerente, Tesoureiro)
 */
router.get('/vaults/:id', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const vault = await vaultService.getVaultById(parseInt(id));
    
    if (!vault) {
      return next(new AppError('Cofre não encontrado', 404, 'VAULT_NOT_FOUND'));
    }

    res.status(200).json({
      status: 'success',
      data: { vault }
    });

  } catch (error) {
    logger.error('Erro ao obter cofre por ID:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * GET /api/system/vaults/:id/movements
 * Obter movimentos de um cofre específico (Admin, Gerente, Tesoureiro)
 */
router.get('/vaults/:id/movements', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  try {
    const { id } = req.params;
    const { page, limit, movement_type, date_from, date_to } = req.query;
    
    const filters = {
      page: page ? parseInt(page) : 1,
      limit: limit ? parseInt(limit) : 20,
      movementType: movement_type,
      dateFrom: date_from,
      dateTo: date_to
    };

    const result = await vaultService.getVaultMovements(parseInt(id), filters);

    res.status(200).json({
      status: 'success',
      data: result
    });

  } catch (error) {
    logger.error('Erro ao obter movimentos do cofre:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

/**
 * GET /api/system/vaults/:id/statistics
 * Obter estatísticas de um cofre específico (Admin, Gerente, Tesoureiro)
 */
router.get('/vaults/:id/statistics', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  try {
    const { id } = req.params;
    const { period = 'month' } = req.query;
    
    const vault = await vaultService.getVaultById(parseInt(id));
    if (!vault) {
      return next(new AppError('Cofre não encontrado', 404, 'VAULT_NOT_FOUND'));
    }

    const statistics = await vaultService.getVaultStatistics(parseInt(id), period);

    res.status(200).json({
      status: 'success',
      data: {
        vault: {
          id: vault.id,
          name: vault.name,
          current_balance: vault.current_balance,
          branch_name: vault.branch_name
        },
        statistics,
        period
      }
    });

  } catch (error) {
    logger.error('Erro ao obter estatísticas do cofre:', error);
    return next(new AppError('Erro interno do servidor', 500, 'INTERNAL_ERROR'));
  }
}));

module.exports = router;
