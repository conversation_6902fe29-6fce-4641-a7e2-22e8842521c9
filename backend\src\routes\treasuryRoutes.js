const express = require('express');
const { catchAsync, AppError } = require('../core/errorHandler');
const { executeQuery } = require('../config/database');
const { authorize } = require('../auth/middleware');
const Joi = require('joi');
const { v4: uuidv4 } = require('uuid');
const logger = require('../core/logger');

const router = express.Router();

// Schemas de validação
const deliverToCashSchema = Joi.object({
  cash_register_id: Joi.number().integer().positive().required(),
  amount: Joi.number().positive().required(),
  denominations: Joi.object({
    notes_10000: Joi.number().integer().min(0).default(0),
    notes_5000: Joi.number().integer().min(0).default(0),
    notes_2000: Joi.number().integer().min(0).default(0),
    notes_1000: Joi.number().integer().min(0).default(0),
    notes_500: Joi.number().integer().min(0).default(0),
    notes_200: Joi.number().integer().min(0).default(0),
    notes_100: Joi.number().integer().min(0).default(0),
    notes_50: Joi.number().integer().min(0).default(0),
    coins_10: Joi.number().integer().min(0).default(0),
    coins_5: Joi.number().integer().min(0).default(0),
    coins_1: Joi.number().integer().min(0).default(0)
  }).required(),
  notes: Joi.string().allow('').max(500)
});

const deliverToVaultSchema = Joi.object({
  amount: Joi.number().positive().required(),
  source_type: Joi.string().valid('cash_register', 'treasury', 'manual', 'system').required(),
  source_id: Joi.string().allow('').max(50),
  denominations: Joi.object({
    notes_10000: Joi.number().integer().min(0).default(0),
    notes_5000: Joi.number().integer().min(0).default(0),
    notes_2000: Joi.number().integer().min(0).default(0),
    notes_1000: Joi.number().integer().min(0).default(0),
    notes_500: Joi.number().integer().min(0).default(0),
    notes_200: Joi.number().integer().min(0).default(0),
    notes_100: Joi.number().integer().min(0).default(0),
    notes_50: Joi.number().integer().min(0).default(0),
    coins_10: Joi.number().integer().min(0).default(0),
    coins_5: Joi.number().integer().min(0).default(0),
    coins_1: Joi.number().integer().min(0).default(0)
  }).required(),
  notes: Joi.string().allow('').max(500)
});

const deliverToTreasurerSchema = Joi.object({
  treasurer_id: Joi.string().uuid().required(),
  amount: Joi.number().positive().required(),
  denominations: Joi.object({
    notes_10000: Joi.number().integer().min(0).default(0),
    notes_5000: Joi.number().integer().min(0).default(0),
    notes_2000: Joi.number().integer().min(0).default(0),
    notes_1000: Joi.number().integer().min(0).default(0),
    notes_500: Joi.number().integer().min(0).default(0),
    notes_200: Joi.number().integer().min(0).default(0),
    notes_100: Joi.number().integer().min(0).default(0),
    notes_50: Joi.number().integer().min(0).default(0),
    coins_10: Joi.number().integer().min(0).default(0),
    coins_5: Joi.number().integer().min(0).default(0),
    coins_1: Joi.number().integer().min(0).default(0)
  }).required(),
  notes: Joi.string().allow('').max(500)
});

const deliverToCounterSchema = Joi.object({
  counter_id: Joi.string().required(),
  counter_name: Joi.string().required(),
  branch_name: Joi.string().required(),
  amount: Joi.number().positive().required(),
  denominations: Joi.object({
    notes_10000: Joi.number().integer().min(0).default(0),
    notes_5000: Joi.number().integer().min(0).default(0),
    notes_2000: Joi.number().integer().min(0).default(0),
    notes_1000: Joi.number().integer().min(0).default(0),
    notes_500: Joi.number().integer().min(0).default(0),
    notes_200: Joi.number().integer().min(0).default(0),
    notes_100: Joi.number().integer().min(0).default(0),
    notes_50: Joi.number().integer().min(0).default(0),
    coins_10: Joi.number().integer().min(0).default(0),
    coins_5: Joi.number().integer().min(0).default(0),
    coins_1: Joi.number().integer().min(0).default(0)
  }).required(),
  notes: Joi.string().allow('').max(500)
});

// Placeholder routes for treasury operations
// TODO: Implement full treasury functionality

/**
 * POST /api/treasury/deliver-to-cash
 * Entrega de valores da tesouraria para caixa
 */
router.post('/deliver-to-cash', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = deliverToCashSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { cash_register_id, amount, denominations, notes } = value;

  // 2. Verificar se caixa existe e está disponível
  const cashRegisters = await executeQuery(
    'SELECT id, register_number, status FROM cash_registers WHERE id = ?',
    [cash_register_id]
  );

  if (!cashRegisters || cashRegisters.length === 0) {
    return next(new AppError('Caixa não encontrado', 404, 'CASH_REGISTER_NOT_FOUND'));
  }

  if (cashRegisters[0].status !== 'available') {
    return next(new AppError('Caixa não está disponível para receber valores', 400, 'CASH_REGISTER_NOT_AVAILABLE'));
  }

  // 3. Calcular total das denominações
  const calculatedTotal = (
    denominations.notes_10000 * 10000 +
    denominations.notes_5000 * 5000 +
    denominations.notes_2000 * 2000 +
    denominations.notes_1000 * 1000 +
    denominations.notes_500 * 500 +
    denominations.notes_200 * 200 +
    denominations.notes_100 * 100 +
    denominations.notes_50 * 50 +
    denominations.coins_10 * 10 +
    denominations.coins_5 * 5 +
    denominations.coins_1 * 1
  );

  // 4. Verificar se total das denominações confere com o valor informado
  if (Math.abs(calculatedTotal - amount) > 0.01) {
    return next(new AppError('Total das denominações não confere com o valor informado', 400, 'DENOMINATION_MISMATCH'));
  }

  // 5. Criar registro de entrega
  const deliveryId = uuidv4();

  await executeQuery(
    `INSERT INTO treasury_deliveries
     (id, cash_register_id, delivered_by, amount, denominations, notes, delivery_type, created_at)
     VALUES (?, ?, ?, ?, ?, ?, 'cash_delivery', NOW())`,
    [deliveryId, cash_register_id, req.user.id, amount, JSON.stringify(denominations), notes || null]
  );

  // 6. Buscar informações completas da entrega
  const delivery = await executeQuery(
    `SELECT td.id, td.amount, td.denominations, td.notes, td.created_at,
            cr.register_number, cr.description as cash_register_description,
            u.full_name as delivered_by_name
     FROM treasury_deliveries td
     JOIN cash_registers cr ON td.cash_register_id = cr.id
     JOIN users u ON td.delivered_by = u.id
     WHERE td.id = ?`,
    [deliveryId]
  );

  logger.info(`Entrega a caixa realizada: ${amount} AOA para ${cashRegisters[0].register_number}`, {
    deliveryId,
    cashRegisterId: cash_register_id,
    amount,
    deliveredBy: req.user.id
  });

  res.status(201).json({
    status: 'success',
    message: 'Entrega a caixa realizada com sucesso',
    data: {
      delivery: {
        ...delivery[0],
        denominations: JSON.parse(delivery[0].denominations)
      }
    }
  });
}));

/**
 * POST /api/treasury/deliver-to-vault
 * Entrega de valores ao cofre principal
 */
router.post('/deliver-to-vault', authorize('tesoureiro'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = deliverToVaultSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { amount, source_type, source_id, denominations, notes } = value;
  const userId = req.user.id;

  // 2. Verificar se existe cofre principal ativo
  const vaults = await executeQuery(
    'SELECT id, vault_code, vault_name, current_balance, capacity FROM main_vaults WHERE is_active = 1 LIMIT 1'
  );

  if (!vaults || vaults.length === 0) {
    return next(new AppError('Nenhum cofre principal ativo encontrado', 404, 'VAULT_NOT_FOUND'));
  }

  const vault = vaults[0];

  // 3. Verificar se o cofre não excederá a capacidade máxima
  const newBalance = parseFloat(vault.current_balance) + amount;
  if (newBalance > parseFloat(vault.capacity)) {
    return next(new AppError(
      `Operação excede a capacidade máxima do cofre (${vault.max_capacity} AOA)`,
      400,
      'VAULT_CAPACITY_EXCEEDED'
    ));
  }

  // 4. Validar denominações (total deve corresponder ao valor)
  const denominationsTotal =
    denominations.notes_10000 * 10000 +
    denominations.notes_5000 * 5000 +
    denominations.notes_2000 * 2000 +
    denominations.notes_1000 * 1000 +
    denominations.notes_500 * 500 +
    denominations.notes_200 * 200 +
    denominations.notes_100 * 100 +
    denominations.notes_50 * 50 +
    denominations.coins_10 * 10 +
    denominations.coins_5 * 5 +
    denominations.coins_1 * 1;

  if (Math.abs(denominationsTotal - amount) > 0.01) {
    return next(new AppError(
      'O total das denominações não corresponde ao valor informado',
      400,
      'DENOMINATIONS_MISMATCH'
    ));
  }

  const referenceNumber = `VLT-${Date.now()}`;

  try {
    // 5. Iniciar transação
    await executeQuery('START TRANSACTION');

    // 6. Registrar movimento no cofre
    await executeQuery(
      `INSERT INTO vault_movements (
        vault_id, movement_type, amount, previous_balance, new_balance,
        source_type, source_id, reference_number, description, notes,
        denominations, user_id, processed_by
      ) VALUES (?, 'deposit', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        vault.id,
        amount,
        vault.current_balance,
        newBalance,
        source_type,
        source_id || null,
        referenceNumber,
        `Entrega ao cofre - ${source_type}`,
        notes || null,
        JSON.stringify(denominations),
        userId,
        userId
      ]
    );

    // 7. Atualizar saldo do cofre
    await executeQuery(
      'UPDATE main_vaults SET current_balance = ?, updated_at = NOW() WHERE id = ?',
      [newBalance, vault.id]
    );

    // 8. Registrar na auditoria
    await executeQuery(
      `INSERT INTO audit_logs (
        user_id, action, table_name, record_id, new_values, ip_address, created_at
      ) VALUES (?, 'VAULT_DEPOSIT', 'vault_movements', ?, ?, ?, NOW())`,
      [
        userId,
        referenceNumber,
        JSON.stringify({
          amount,
          source_type,
          source_id,
          vault_code: vault.vault_code,
          previous_balance: vault.current_balance,
          new_balance: newBalance,
          reference_number: referenceNumber
        }),
        req.ip || req.connection.remoteAddress
      ]
    );

    // 9. Confirmar transação
    await executeQuery('COMMIT');

    // 10. Buscar dados completos do movimento criado
    const movement = await executeQuery(
      `SELECT
        vm.id, vm.vault_id, vm.movement_type, vm.amount,
        vm.previous_balance, vm.new_balance, vm.denominations,
        vm.reference_number, vm.description, vm.notes,
        vm.source_type, vm.source_id, vm.created_at
      FROM vault_movements vm
      WHERE vm.reference_number = ?
      ORDER BY vm.created_at DESC
      LIMIT 1`,
      [referenceNumber]
    );

    logger.info(`Entrega ao cofre realizada: ${amount} AOA por usuário ${userId}`);

    res.status(201).json({
      status: 'success',
      message: 'Entrega ao cofre realizada com sucesso',
      data: {
        movement: {
          ...movement[0],
          denominations: JSON.parse(movement[0].denominations)
        }
      }
    });

  } catch (error) {
    // Rollback em caso de erro
    await executeQuery('ROLLBACK');
    logger.error('Erro ao processar entrega ao cofre:', error);
    throw error;
  }
}));

/**
 * POST /api/treasury/deliver-to-treasurer
 * Entrega de valores do cofre principal para tesoureiro
 */
router.post('/deliver-to-treasurer', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = deliverToTreasurerSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { treasurer_id, amount, denominations, notes } = value;
  const userId = req.user.id;

  // 2. Verificar se tesoureiro existe e está ativo
  const treasurers = await executeQuery(`
    SELECT u.id, u.full_name, u.email, u.is_active, r.name as role_name, b.name as branch_name
    FROM users u
    JOIN roles r ON u.role_id = r.id
    LEFT JOIN branches b ON u.branch_id = b.id
    WHERE u.id = ? AND r.name = 'tesoureiro' AND u.is_active = 1
  `, [treasurer_id]);

  if (!treasurers || treasurers.length === 0) {
    return next(new AppError('Tesoureiro não encontrado ou inativo', 404, 'TREASURER_NOT_FOUND'));
  }

  const treasurer = treasurers[0];

  // 3. Verificar se existe cofre principal ativo
  const vaults = await executeQuery(
    'SELECT id, vault_code, vault_name, current_balance, capacity FROM main_vaults WHERE is_active = 1 LIMIT 1'
  );

  if (!vaults || vaults.length === 0) {
    return next(new AppError('Cofre principal não encontrado', 404, 'VAULT_NOT_FOUND'));
  }

  const vault = vaults[0];

  // 4. Verificar se há saldo suficiente no cofre
  if (parseFloat(vault.current_balance) < amount) {
    return next(new AppError(
      `Saldo insuficiente no cofre. Disponível: ${vault.current_balance} AOA, Solicitado: ${amount} AOA`,
      400,
      'INSUFFICIENT_VAULT_BALANCE'
    ));
  }

  // 5. Validar denominações (soma deve ser igual ao valor)
  const denominationTotal =
    (denominations.notes_10000 * 10000) +
    (denominations.notes_5000 * 5000) +
    (denominations.notes_2000 * 2000) +
    (denominations.notes_1000 * 1000) +
    (denominations.notes_500 * 500) +
    (denominations.notes_200 * 200) +
    (denominations.notes_100 * 100) +
    (denominations.notes_50 * 50) +
    (denominations.coins_10 * 10) +
    (denominations.coins_5 * 5) +
    (denominations.coins_1 * 1);

  if (Math.abs(denominationTotal - amount) > 0.01) {
    return next(new AppError(
      `Valor das denominações (${denominationTotal}) não confere com o valor informado (${amount})`,
      400,
      'DENOMINATION_MISMATCH'
    ));
  }

  try {
    // 6. Iniciar transação
    await executeQuery('START TRANSACTION');

    const deliveryId = uuidv4();
    const referenceNumber = `TRS-${Date.now()}`;
    const previousBalance = parseFloat(vault.current_balance);
    const newBalance = previousBalance - amount;

    // 7. Registrar entrega na tabela treasury_deliveries
    await executeQuery(`
      INSERT INTO treasury_deliveries (
        id, vault_id, delivered_by, amount, denominations, notes,
        delivery_type, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'treasurer_delivery', 'pending', NOW())
    `, [
      deliveryId,
      vault.id,
      userId,
      amount,
      JSON.stringify(denominations),
      notes || null
    ]);

    // 8. Atualizar saldo do cofre
    await executeQuery(
      'UPDATE main_vaults SET current_balance = ?, updated_at = NOW() WHERE id = ?',
      [newBalance, vault.id]
    );

    // 9. Registrar movimento no cofre
    await executeQuery(`
      INSERT INTO vault_movements (
        id, vault_id, movement_type, amount, previous_balance, new_balance,
        denominations, reference_number, description, notes,
        source_type, source_id, processed_by, created_at
      ) VALUES (?, ?, 'withdrawal', ?, ?, ?, ?, ?, ?, ?, 'treasurer', ?, ?, NOW())
    `, [
      uuidv4(),
      vault.id,
      amount,
      previousBalance,
      newBalance,
      JSON.stringify(denominations),
      referenceNumber,
      `Entrega ao tesoureiro: ${treasurer.full_name}`,
      notes || null,
      treasurer_id,
      userId
    ]);

    // 10. Registrar log de auditoria
    await executeQuery(`
      INSERT INTO audit_logs (
        id, user_id, action, table_name, record_id, ip_address,
        old_values, new_values, created_at
      ) VALUES (?, ?, 'TREASURER_DELIVERY', 'treasury_deliveries', ?, ?, ?, ?, NOW())
    `, [
      uuidv4(),
      userId,
      deliveryId,
      req.ip || '::1',
      JSON.stringify({ vault_balance: previousBalance }),
      JSON.stringify({
        amount,
        treasurer_id,
        treasurer_name: treasurer.full_name,
        vault_balance: newBalance,
        reference_number: referenceNumber
      })
    ]);

    // 11. Confirmar transação
    await executeQuery('COMMIT');

    // 12. Buscar dados da entrega criada
    const delivery = await executeQuery(`
      SELECT
        td.id, td.amount, td.denominations, td.notes, td.delivery_type,
        td.status, td.created_at, u.full_name as treasurer_name,
        u.email as treasurer_email, mv.vault_name
      FROM treasury_deliveries td
      JOIN users u ON u.id = ?
      JOIN main_vaults mv ON td.vault_id = mv.id
      WHERE td.id = ?
    `, [treasurer_id, deliveryId]);

    logger.info(`Entrega ao tesoureiro realizada: ${amount} AOA para ${treasurer.full_name} por usuário ${userId}`);

    res.status(201).json({
      status: 'success',
      message: 'Entrega ao tesoureiro realizada com sucesso',
      data: {
        delivery: {
          ...delivery[0],
          denominations: JSON.parse(delivery[0].denominations),
          reference_number: referenceNumber
        }
      }
    });

  } catch (error) {
    // Rollback em caso de erro
    await executeQuery('ROLLBACK');
    logger.error('Erro ao processar entrega ao tesoureiro:', error);
    throw error;
  }
}));

/**
 * POST /api/treasury/load-atm
 * Carregamento de ATM
 */
router.post('/load-atm', authorize('tesoureiro'), (req, res) => {
  res.status(201).json({
    status: 'success',
    message: 'Carregamento de ATM - Em desenvolvimento'
  });
});

/**
 * POST /api/treasury/deliver-to-counter
 * Entrega de valores do cofre principal para balcão
 */
router.post('/deliver-to-counter', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = deliverToCounterSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { counter_id, counter_name, branch_name, amount, denominations, notes } = value;
  const userId = req.user.id;

  try {
    // 2. Verificar se o cofre principal existe e tem saldo suficiente
    const vault = await executeQuery(
      'SELECT * FROM main_vaults WHERE is_active = 1 LIMIT 1'
    );

    if (!vault || vault.length === 0) {
      return next(new AppError('Cofre principal não encontrado para esta agência', 404, 'VAULT_NOT_FOUND'));
    }

    const currentBalance = parseFloat(vault[0].current_balance);
    if (currentBalance < amount) {
      return next(new AppError(
        `Saldo insuficiente no cofre. Saldo atual: ${currentBalance.toFixed(2)} AOA, Valor solicitado: ${amount.toFixed(2)} AOA`,
        400,
        'INSUFFICIENT_BALANCE'
      ));
    }

    // 3. Validar denominações
    const denominationsTotal = Object.entries(denominations).reduce((total, [key, quantity]) => {
      const value = key.includes('notes_') ?
        parseInt(key.replace('notes_', '')) :
        parseInt(key.replace('coins_', ''));
      return total + (quantity * value);
    }, 0);

    if (Math.abs(denominationsTotal - amount) > 0.01) {
      return next(new AppError(
        `Total das denominações (${denominationsTotal.toFixed(2)}) não confere com o valor informado (${amount.toFixed(2)})`,
        400,
        'DENOMINATIONS_MISMATCH'
      ));
    }

    // 4. Iniciar transação
    await executeQuery('START TRANSACTION');

    const deliveryId = uuidv4();
    const referenceNumber = `CTR-${Date.now()}`;
    const previousBalance = parseFloat(vault[0].current_balance);
    const newBalance = previousBalance - amount;

    // 5. Registrar entrega na tabela treasury_deliveries
    await executeQuery(`
      INSERT INTO treasury_deliveries (
        id, vault_id, delivered_by, amount, denominations, notes,
        delivery_type, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'counter_delivery', 'confirmed', NOW())
    `, [
      deliveryId,
      vault[0].id,
      userId,
      amount,
      JSON.stringify(denominations),
      notes || null
    ]);

    // 6. Atualizar saldo do cofre
    await executeQuery(
      'UPDATE main_vaults SET current_balance = ?, updated_at = NOW() WHERE id = ?',
      [newBalance, vault[0].id]
    );

    // 7. Registrar movimento no cofre
    await executeQuery(`
      INSERT INTO vault_movements (
        vault_id, movement_type, amount, previous_balance, new_balance,
        source_type, source_id, reference_number, description, notes,
        denominations, processed_by, created_at
      ) VALUES (?, 'withdrawal', ?, ?, ?, 'counter', ?, ?, ?, ?, ?, ?, NOW())
    `, [
      vault[0].id,
      amount,
      previousBalance,
      newBalance,
      counter_id,
      referenceNumber,
      `Entrega ao balcão: ${counter_name} - ${branch_name}`,
      notes || null,
      JSON.stringify(denominations),
      userId
    ]);

    // 8. Registrar na auditoria
    await executeQuery(
      `INSERT INTO audit_logs (
        user_id, action, table_name, record_id, new_values, ip_address, created_at
      ) VALUES (?, 'COUNTER_DELIVERY', 'treasury_deliveries', ?, ?, ?, NOW())`,
      [
        userId,
        deliveryId,
        JSON.stringify({
          counter_id,
          counter_name,
          branch_name,
          amount,
          vault_code: vault[0].vault_code,
          previous_balance: previousBalance,
          new_balance: newBalance,
          reference_number: referenceNumber
        }),
        req.ip || req.connection.remoteAddress
      ]
    );

    // 9. Confirmar transação
    await executeQuery('COMMIT');

    // 10. Buscar dados da entrega criada
    const delivery = await executeQuery(`
      SELECT
        td.*,
        u.full_name as delivered_by_name,
        mv.vault_code,
        mv.vault_name
      FROM treasury_deliveries td
      JOIN users u ON u.id = ?
      JOIN main_vaults mv ON td.vault_id = mv.id
      WHERE td.id = ?
    `, [userId, deliveryId]);

    logger.info(`Entrega ao balcão realizada: ${amount} AOA para ${counter_name} por usuário ${userId}`);

    res.status(201).json({
      status: 'success',
      message: 'Entrega ao balcão realizada com sucesso',
      data: {
        delivery: {
          ...delivery[0],
          denominations: JSON.parse(delivery[0].denominations),
          reference_number: referenceNumber,
          counter_id,
          counter_name,
          branch_name
        }
      }
    });

  } catch (error) {
    // Rollback em caso de erro
    await executeQuery('ROLLBACK');
    logger.error('Erro ao processar entrega ao balcão:', error);
    throw error;
  }
}));

module.exports = router;
