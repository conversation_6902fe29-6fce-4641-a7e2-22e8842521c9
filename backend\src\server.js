const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const logger = require('./core/logger');
const { errorHandler } = require('./core/errorHandler');
const { authenticate } = require('./auth/middleware');
const { initializeDatabase } = require('./config/database');

// Importar rotas
const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const auditRoutes = require('./routes/auditRoutes');
const branchRoutes = require('./routes/branchRoutes');
const roleRoutes = require('./routes/roleRoutes');
const clientRoutes = require('./routes/clientRoutes');
const accountRoutes = require('./routes/accountRoutes');
const approvalRoutes = require('./routes/approvalRoutes');
const cashRegisterRoutes = require('./routes/cashRegisterRoutes');
const treasuryRoutes = require('./routes/treasuryRoutes');
const transferRoutes = require('./routes/transferRoutes');
const movementRoutes = require('./routes/movementRoutes');
const cardRoutes = require('./routes/cardRoutes');
const exchangeRoutes = require('./routes/exchangeRoutes');
const insuranceRoutes = require('./routes/insuranceRoutes');
const atmRoutes = require('./routes/atmRoutes');
const reportRoutes = require('./routes/reportRoutes');
const systemRoutes = require('./routes/systemRoutes');
const uploadRoutes = require('./routes/uploadRoutes');

const app = express();
const PORT = process.env.PORT || 3001;

// Configuração de Rate Limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutos
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // máximo 100 requests por janela
  message: {
    error: 'Muitas tentativas. Tente novamente mais tarde.',
    retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000) / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware de segurança
app.use(helmet());

// Aplicar rate limiting apenas em produção
if (process.env.NODE_ENV !== 'development') {
  app.use(limiter);
  logger.info('Rate limiting ativado para ambiente de produção');
} else {
  logger.info('Rate limiting desabilitado para ambiente de desenvolvimento');
}

// Configuração CORS
const corsOrigins = (process.env.CORS_ORIGIN || 'http://localhost:5173')
  .split(',')
  .map(origin => origin.trim());

app.use(cors({
  origin: (origin, callback) => {
    // Permitir requisições sem origin (ex: aplicações mobile, Postman)
    if (!origin) return callback(null, true);

    // Verificar se a origin está na lista permitida
    if (corsOrigins.includes(origin)) {
      return callback(null, true);
    }

    // Permitir localhost e IPs locais em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      // Permitir localhost
      if (origin.includes('localhost')) {
        return callback(null, true);
      }

      // Permitir IPs da rede local (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
      const localIpPattern = /^https?:\/\/(192\.168\.\d{1,3}\.\d{1,3}|10\.\d{1,3}\.\d{1,3}\.\d{1,3}|172\.(1[6-9]|2\d|3[01])\.\d{1,3}\.\d{1,3})(:\d+)?$/;
      if (localIpPattern.test(origin)) {
        return callback(null, true);
      }
    }

    return callback(new Error('Não permitido pelo CORS'), false);
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  optionsSuccessStatus: 200 // Para suportar browsers legados
}));

// Middleware de parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Servir arquivos estáticos (uploads)
app.use('/uploads', express.static('backend/uploads'));

// Middleware de logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// Rotas públicas (sem autenticação)
app.use('/api/auth', authRoutes);

// Health check endpoints
const { checkDatabaseHealth } = require('./config/database');

// Basic health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: require('../package.json').version,
    uptime: process.uptime()
  });
});

// Detailed health check with database
app.get('/api/health/detailed', async (req, res) => {
  try {
    const dbHealth = await checkDatabaseHealth();

    res.json({
      status: dbHealth.status === 'healthy' ? 'OK' : 'DEGRADED',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      version: require('../package.json').version,
      uptime: process.uptime(),
      database: dbHealth,
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + ' MB',
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024) + ' MB'
      }
    });
  } catch (error) {
    logger.error('Erro no health check detalhado:', error);
    res.status(503).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: 'Falha na verificação de saúde do sistema',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Middleware de autenticação para rotas protegidas
app.use('/api', authenticate);

// Rotas protegidas
app.use('/api/users', userRoutes);
app.use('/api/audit', auditRoutes);
app.use('/api/branches', branchRoutes);
app.use('/api/roles', roleRoutes);
app.use('/api/clients', clientRoutes);
app.use('/api/accounts', accountRoutes);
app.use('/api/approvals', approvalRoutes);
app.use('/api/cash-registers', cashRegisterRoutes);
app.use('/api/treasury', treasuryRoutes);
app.use('/api/transfers', transferRoutes);
app.use('/api/movements', movementRoutes);
app.use('/api/cards', cardRoutes);
app.use('/api/exchange', exchangeRoutes);
app.use('/api/insurance', insuranceRoutes);
app.use('/api/atm', atmRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/system', systemRoutes);
app.use('/api/upload', uploadRoutes);

// Middleware de tratamento de erros
app.use(errorHandler);

// Rota 404
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Rota não encontrada',
    path: req.originalUrl,
    method: req.method
  });
});

// Iniciar servidor
const startServer = async () => {
  try {
    // Inicializar base de dados com retry logic
    await initializeDatabase();

    // Iniciar servidor
    app.listen(PORT, () => {
      logger.info(`Servidor Twins_Bank iniciado na porta ${PORT}`);
      logger.info(`Ambiente: ${process.env.NODE_ENV}`);
      logger.info(`CORS configurado para: ${process.env.CORS_ORIGIN}`);
      logger.info(`Rate limiting: ${process.env.NODE_ENV !== 'development' ? 'ATIVADO' : 'DESABILITADO'}`);
    });
  } catch (error) {
    logger.error('Erro ao iniciar servidor:', error);
    logger.error('Verifique a configuração da base de dados no arquivo .env');
    process.exit(1);
  }
};

startServer();

// Tratamento de erros não capturados
process.on('uncaughtException', (error) => {
  logger.error('Erro não capturado:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Promise rejeitada não tratada:', reason);
  process.exit(1);
});

module.exports = app;
