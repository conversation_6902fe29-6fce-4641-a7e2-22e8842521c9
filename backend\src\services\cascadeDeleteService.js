const { executeQuery, executeTransaction } = require('../config/database');
const logger = require('../core/logger');
const { deleteClientDocuments } = require('../core/supabaseUpload');

/**
 * Serviço para eliminação em cascata de clientes e dados relacionados
 */
class CascadeDeleteService {
  
  /**
   * Eliminar cliente e todos os dados relacionados em cascata
   * @param {string} clientId - ID do cliente
   * @param {Object} user - Utilizador que está a eliminar
   * @returns {Promise<Object>} Resultado da eliminação
   */
  async deleteClientCascade(clientId, user) {
    try {
      logger.info(`Iniciando eliminação em cascata do cliente: ${clientId}`, {
        clientId,
        deletedBy: user.id,
        deletedByName: user.full_name
      });

      // 1. Verificar se cliente existe e obter informações
      const clientInfo = await this.getClientInfo(clientId);
      if (!clientInfo) {
        throw new Error('Cliente não encontrado');
      }

      // 2. Obter todas as contas do cliente
      const clientAccounts = await this.getClientAccounts(clientId);
      
      // 3. Obter estatísticas antes da eliminação
      const deletionStats = await this.getDeletionStats(clientId, clientAccounts);

      // 4. Executar eliminação em cascata dentro de uma transação
      const result = await executeTransaction(async (connection) => {
        // 4.1. Eliminar transações relacionadas às contas do cliente
        await this.deleteClientTransactions(connection, clientAccounts);

        // 4.2. Eliminar cartões das contas do cliente
        await this.deleteClientCards(connection, clientAccounts);

        // 4.3. Eliminar saldos das contas
        await this.deleteAccountBalances(connection, clientAccounts);

        // 4.4. Eliminar titulares das contas
        await this.deleteAccountHolders(connection, clientId);

        // 4.5. Eliminar aplicações de conta
        await this.deleteAccountApplications(connection, clientId);

        // 4.6. Eliminar contas
        await this.deleteAccounts(connection, clientAccounts);

        // 4.7. Eliminar documentos da base de dados
        await this.deleteClientDocumentsDB(connection, clientId);

        // 4.8. Eliminar endereços do cliente
        await this.deleteClientAddresses(connection, clientId);

        // 4.9. Eliminar contactos do cliente
        await this.deleteClientContacts(connection, clientId);

        // 4.10. Eliminar apólices de seguro
        await this.deleteInsurancePolicies(connection, clientId);

        // 4.11. Eliminar cliente
        await this.deleteClient(connection, clientId);

        return {
          clientId,
          clientName: clientInfo.full_name || clientInfo.company_name,
          clientType: clientInfo.client_type,
          deletionStats
        };
      });

      // 5. Eliminar documentos do Supabase (fora da transação)
      try {
        await deleteClientDocuments(clientId, clientInfo.client_type);
        logger.info(`Documentos do Supabase eliminados para cliente: ${clientId}`);
      } catch (supabaseError) {
        logger.warn(`Erro ao eliminar documentos do Supabase para cliente ${clientId}:`, supabaseError);
        // Não falhar a operação se Supabase falhar
      }

      // 6. Log final de sucesso
      logger.info(`Cliente eliminado em cascata com sucesso: ${result.clientName}`, {
        ...result,
        deletedBy: user.id,
        deletedByName: user.full_name
      });

      return {
        status: 'success',
        message: 'Cliente e todos os dados relacionados eliminados com sucesso',
        data: result
      };

    } catch (error) {
      logger.error(`Erro na eliminação em cascata do cliente ${clientId}:`, error);
      throw new Error(`Erro na eliminação em cascata: ${error.message}`);
    }
  }

  /**
   * Obter informações do cliente
   */
  async getClientInfo(clientId) {
    const clients = await executeQuery(
      'SELECT id, full_name, company_name, client_type FROM clients WHERE id = ?',
      [clientId]
    );
    return clients && clients.length > 0 ? clients[0] : null;
  }

  /**
   * Obter todas as contas do cliente
   */
  async getClientAccounts(clientId) {
    const accounts = await executeQuery(`
      SELECT DISTINCT a.id, a.account_number, a.account_type, a.status
      FROM accounts a
      INNER JOIN account_holders ah ON a.id = ah.account_id
      WHERE ah.client_id = ?
    `, [clientId]);
    
    return accounts || [];
  }

  /**
   * Obter estatísticas da eliminação
   */
  async getDeletionStats(clientId, clientAccounts) {
    const accountIds = clientAccounts.map(acc => acc.id);
    
    if (accountIds.length === 0) {
      return {
        accounts: 0,
        transactions: 0,
        cards: 0,
        documents: 0,
        addresses: 0,
        contacts: 0
      };
    }

    const placeholders = accountIds.map(() => '?').join(',');
    
    // Contar transações
    const transactionCount = await executeQuery(`
      SELECT COUNT(*) as count FROM transactions 
      WHERE source_account_id IN (${placeholders}) OR destination_account_id IN (${placeholders})
    `, [...accountIds, ...accountIds]);

    // Contar cartões
    const cardCount = await executeQuery(`
      SELECT COUNT(*) as count FROM cards WHERE account_id IN (${placeholders})
    `, accountIds);

    // Contar documentos
    const documentCount = await executeQuery(
      'SELECT COUNT(*) as count FROM client_documents WHERE client_id = ?',
      [clientId]
    );

    // Contar endereços
    const addressCount = await executeQuery(
      'SELECT COUNT(*) as count FROM client_addresses WHERE client_id = ?',
      [clientId]
    );

    // Contar contactos
    const contactCount = await executeQuery(
      'SELECT COUNT(*) as count FROM client_contacts WHERE client_id = ?',
      [clientId]
    );

    return {
      accounts: clientAccounts.length,
      transactions: transactionCount[0]?.count || 0,
      cards: cardCount[0]?.count || 0,
      documents: documentCount[0]?.count || 0,
      addresses: addressCount[0]?.count || 0,
      contacts: contactCount[0]?.count || 0
    };
  }

  /**
   * Eliminar transações das contas do cliente
   */
  async deleteClientTransactions(connection, clientAccounts) {
    if (clientAccounts.length === 0) return;

    const accountIds = clientAccounts.map(acc => acc.id);
    const placeholders = accountIds.map(() => '?').join(',');

    await connection.execute(
      `DELETE FROM transactions WHERE source_account_id IN (${placeholders}) OR destination_account_id IN (${placeholders})`,
      [...accountIds, ...accountIds]
    );

    logger.info(`Transações eliminadas para ${accountIds.length} contas`);
  }

  /**
   * Eliminar cartões das contas do cliente
   */
  async deleteClientCards(connection, clientAccounts) {
    if (clientAccounts.length === 0) return;

    const accountIds = clientAccounts.map(acc => acc.id);
    const placeholders = accountIds.map(() => '?').join(',');

    await connection.execute(
      `DELETE FROM cards WHERE account_id IN (${placeholders})`,
      accountIds
    );

    logger.info(`Cartões eliminados para ${accountIds.length} contas`);
  }

  /**
   * Eliminar saldos das contas
   */
  async deleteAccountBalances(connection, clientAccounts) {
    if (clientAccounts.length === 0) return;

    const accountIds = clientAccounts.map(acc => acc.id);
    const placeholders = accountIds.map(() => '?').join(',');

    await connection.execute(
      `DELETE FROM account_balances WHERE account_id IN (${placeholders})`,
      accountIds
    );

    logger.info(`Saldos eliminados para ${accountIds.length} contas`);
  }

  /**
   * Eliminar titulares das contas
   */
  async deleteAccountHolders(connection, clientId) {
    await connection.execute(
      'DELETE FROM account_holders WHERE client_id = ?',
      [clientId]
    );

    logger.info(`Titulares de conta eliminados para cliente: ${clientId}`);
  }

  /**
   * Eliminar aplicações de conta
   */
  async deleteAccountApplications(connection, clientId) {
    await connection.execute(
      'DELETE FROM account_applications WHERE client_id = ?',
      [clientId]
    );

    logger.info(`Aplicações de conta eliminadas para cliente: ${clientId}`);
  }

  /**
   * Eliminar contas
   */
  async deleteAccounts(connection, clientAccounts) {
    if (clientAccounts.length === 0) return;

    const accountIds = clientAccounts.map(acc => acc.id);
    const placeholders = accountIds.map(() => '?').join(',');

    await connection.execute(
      `DELETE FROM accounts WHERE id IN (${placeholders})`,
      accountIds
    );

    logger.info(`${accountIds.length} contas eliminadas`);
  }

  /**
   * Eliminar documentos da base de dados
   */
  async deleteClientDocumentsDB(connection, clientId) {
    await connection.execute(
      'DELETE FROM client_documents WHERE client_id = ?',
      [clientId]
    );

    logger.info(`Documentos da BD eliminados para cliente: ${clientId}`);
  }

  /**
   * Eliminar endereços do cliente
   */
  async deleteClientAddresses(connection, clientId) {
    await connection.execute(
      'DELETE FROM client_addresses WHERE client_id = ?',
      [clientId]
    );

    logger.info(`Endereços eliminados para cliente: ${clientId}`);
  }

  /**
   * Eliminar contactos do cliente
   */
  async deleteClientContacts(connection, clientId) {
    await connection.execute(
      'DELETE FROM client_contacts WHERE client_id = ?',
      [clientId]
    );

    logger.info(`Contactos eliminados para cliente: ${clientId}`);
  }

  /**
   * Eliminar apólices de seguro
   */
  async deleteInsurancePolicies(connection, clientId) {
    await connection.execute(
      'DELETE FROM insurance_policies WHERE client_id = ?',
      [clientId]
    );

    logger.info(`Apólices de seguro eliminadas para cliente: ${clientId}`);
  }

  /**
   * Eliminar cliente
   */
  async deleteClient(connection, clientId) {
    await connection.execute(
      'DELETE FROM clients WHERE id = ?',
      [clientId]
    );

    logger.info(`Cliente eliminado: ${clientId}`);
  }
}

module.exports = new CascadeDeleteService();
