const { v4: uuidv4 } = require('uuid');
const { executeQuery } = require('../config/database');
const logger = require('../core/logger');

/**
 * Serviço para gestão dos cofres principais
 */
class VaultService {
  
  /**
   * Obter todos os cofres principais
   * @param {Object} filters - Filtros de pesquisa
   * @returns {Promise<Array>} Lista de cofres
   */
  async getAllVaults(filters = {}) {
    try {
      let whereConditions = [];
      let queryParams = [];

      // Filtro por agência
      if (filters.branch_id) {
        whereConditions.push('mv.branch_id = ?');
        queryParams.push(filters.branch_id);
      }

      // Filtro por status ativo
      if (filters.is_active !== undefined) {
        whereConditions.push('mv.is_active = ?');
        queryParams.push(filters.is_active);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      const vaults = await executeQuery(
        `SELECT 
          mv.id,
          mv.vault_code,
          mv.name,
          mv.description,
          mv.current_balance,
          mv.max_capacity,
          mv.is_active,
          mv.created_at,
          mv.updated_at,
          b.name as branch_name,
          b.code as branch_code
        FROM main_vaults mv
        LEFT JOIN branches b ON mv.branch_id = b.id
        ${whereClause}
        ORDER BY b.code, mv.vault_code`,
        queryParams
      );

      return vaults;
    } catch (error) {
      logger.error('Erro ao obter cofres principais:', error);
      throw error;
    }
  }

  /**
   * Obter cofre por ID
   * @param {number} vaultId - ID do cofre
   * @returns {Promise<Object|null>} Dados do cofre
   */
  async getVaultById(vaultId) {
    try {
      const vaults = await executeQuery(
        `SELECT 
          mv.id,
          mv.vault_code,
          mv.name,
          mv.description,
          mv.current_balance,
          mv.max_capacity,
          mv.is_active,
          mv.created_at,
          mv.updated_at,
          b.name as branch_name,
          b.code as branch_code,
          b.id as branch_id
        FROM main_vaults mv
        LEFT JOIN branches b ON mv.branch_id = b.id
        WHERE mv.id = ?`,
        [vaultId]
      );

      return vaults.length > 0 ? vaults[0] : null;
    } catch (error) {
      logger.error('Erro ao obter cofre por ID:', error);
      throw error;
    }
  }

  /**
   * Obter cofre por código
   * @param {string} vaultCode - Código do cofre
   * @returns {Promise<Object|null>} Dados do cofre
   */
  async getVaultByCode(vaultCode) {
    try {
      const vaults = await executeQuery(
        `SELECT 
          mv.id,
          mv.vault_code,
          mv.name,
          mv.description,
          mv.current_balance,
          mv.max_capacity,
          mv.is_active,
          mv.created_at,
          mv.updated_at,
          b.name as branch_name,
          b.code as branch_code,
          b.id as branch_id
        FROM main_vaults mv
        LEFT JOIN branches b ON mv.branch_id = b.id
        WHERE mv.vault_code = ?`,
        [vaultCode]
      );

      return vaults.length > 0 ? vaults[0] : null;
    } catch (error) {
      logger.error('Erro ao obter cofre por código:', error);
      throw error;
    }
  }

  /**
   * Registar movimento no cofre
   * @param {Object} movementData - Dados do movimento
   * @returns {Promise<Object>} Dados do movimento criado
   */
  async recordMovement(movementData) {
    try {
      const {
        vaultId,
        movementType,
        amount,
        denominations = null,
        referenceNumber = null,
        description = null,
        notes = null,
        sourceType = null,
        sourceId = null,
        destinationType = null,
        destinationId = null,
        processedBy,
        authorizedBy = null
      } = movementData;

      // Obter saldo atual do cofre
      const vault = await this.getVaultById(vaultId);
      if (!vault) {
        throw new Error('Cofre não encontrado');
      }

      const previousBalance = parseFloat(vault.current_balance);
      let newBalance;

      // Calcular novo saldo baseado no tipo de movimento
      switch (movementType) {
        case 'initial_balance':
        case 'deposit':
        case 'transfer_in':
          newBalance = previousBalance + amount;
          break;
        case 'withdrawal':
        case 'transfer_out':
          newBalance = previousBalance - amount;
          if (newBalance < 0) {
            throw new Error('Saldo insuficiente no cofre');
          }
          break;
        case 'adjustment':
          newBalance = amount; // Para ajustes, o amount é o novo saldo
          break;
        default:
          throw new Error('Tipo de movimento inválido');
      }

      // Verificar capacidade máxima
      if (newBalance > vault.max_capacity) {
        throw new Error('Movimento excede a capacidade máxima do cofre');
      }

      const movementId = uuidv4();

      // Inserir movimento
      await executeQuery(
        `INSERT INTO vault_movements (
          id, vault_id, movement_type, amount, previous_balance, new_balance,
          denominations, reference_number, description, notes,
          source_type, source_id, destination_type, destination_id,
          processed_by, authorized_by, status, processed_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'completed', NOW())`,
        [
          movementId, vaultId, movementType, amount, previousBalance, newBalance,
          denominations ? JSON.stringify(denominations) : null,
          referenceNumber, description, notes,
          sourceType, sourceId, destinationType, destinationId,
          processedBy, authorizedBy
        ]
      );

      // Atualizar saldo do cofre
      await executeQuery(
        'UPDATE main_vaults SET current_balance = ?, updated_at = NOW() WHERE id = ?',
        [newBalance, vaultId]
      );

      // Inserir denominações se fornecidas
      if (denominations && typeof denominations === 'object') {
        for (const [key, quantity] of Object.entries(denominations)) {
          if (quantity > 0) {
            const denominationValue = key.includes('notes_') ?
              parseInt(key.replace('notes_', '')) :
              parseInt(key.replace('coins_', ''));

            const denominationType = key.includes('notes_') ? 'note' : 'coin';

            await executeQuery(
              `INSERT INTO vault_movement_denominations
               (movement_id, denomination_type, denomination_value, quantity, total_value)
               VALUES (?, ?, ?, ?, ?)`,
              [movementId, denominationType, denominationValue, quantity, quantity * denominationValue]
            );
          }
        }
      }

      // Retornar dados do movimento criado
      const movements = await executeQuery(
        `SELECT 
          vm.*,
          u.full_name as processed_by_name,
          au.full_name as authorized_by_name
        FROM vault_movements vm
        LEFT JOIN users u ON vm.processed_by = u.id
        LEFT JOIN users au ON vm.authorized_by = au.id
        WHERE vm.id = ?`,
        [movementId]
      );

      return movements[0];
    } catch (error) {
      logger.error('Erro ao registar movimento no cofre:', error);
      throw error;
    }
  }

  /**
   * Obter movimentos do cofre
   * @param {number} vaultId - ID do cofre
   * @param {Object} filters - Filtros de pesquisa
   * @returns {Promise<Object>} Lista de movimentos com paginação
   */
  async getVaultMovements(vaultId, filters = {}) {
    try {
      const { page = 1, limit = 20, movementType, dateFrom, dateTo } = filters;
      const offset = (page - 1) * limit;

      let whereConditions = ['vm.vault_id = ?'];
      let queryParams = [vaultId];

      // Filtro por tipo de movimento
      if (movementType) {
        whereConditions.push('vm.movement_type = ?');
        queryParams.push(movementType);
      }

      // Filtro por data
      if (dateFrom) {
        whereConditions.push('DATE(vm.processed_at) >= ?');
        queryParams.push(dateFrom);
      }

      if (dateTo) {
        whereConditions.push('DATE(vm.processed_at) <= ?');
        queryParams.push(dateTo);
      }

      const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

      // Obter movimentos
      const movements = await executeQuery(
        `SELECT 
          vm.*,
          u.full_name as processed_by_name,
          au.full_name as authorized_by_name
        FROM vault_movements vm
        LEFT JOIN users u ON vm.processed_by = u.id
        LEFT JOIN users au ON vm.authorized_by = au.id
        ${whereClause}
        ORDER BY vm.processed_at DESC
        LIMIT ? OFFSET ?`,
        [...queryParams, parseInt(limit), parseInt(offset)]
      );

      // Contar total
      const totalResult = await executeQuery(
        `SELECT COUNT(*) as total FROM vault_movements vm ${whereClause}`,
        queryParams
      );

      const total = totalResult[0]?.total || 0;
      const totalPages = Math.ceil(total / limit);

      return {
        movements,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      logger.error('Erro ao obter movimentos do cofre:', error);
      throw error;
    }
  }

  /**
   * Obter estatísticas do cofre
   * @param {number} vaultId - ID do cofre
   * @param {string} period - Período (today, week, month, year)
   * @returns {Promise<Object>} Estatísticas do cofre
   */
  async getVaultStatistics(vaultId, period = 'month') {
    try {
      let dateCondition = '';
      
      switch (period) {
        case 'today':
          dateCondition = 'DATE(vm.processed_at) = CURDATE()';
          break;
        case 'week':
          dateCondition = 'vm.processed_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
          break;
        case 'month':
          dateCondition = 'vm.processed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
          break;
        case 'year':
          dateCondition = 'vm.processed_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)';
          break;
        default:
          dateCondition = '1=1'; // Todos os registos
      }

      const stats = await executeQuery(
        `SELECT 
          COUNT(*) as total_movements,
          SUM(CASE WHEN vm.movement_type IN ('deposit', 'transfer_in', 'initial_balance') THEN vm.amount ELSE 0 END) as total_deposits,
          SUM(CASE WHEN vm.movement_type IN ('withdrawal', 'transfer_out') THEN vm.amount ELSE 0 END) as total_withdrawals,
          AVG(vm.amount) as average_movement,
          MAX(vm.amount) as largest_movement,
          MIN(vm.amount) as smallest_movement
        FROM vault_movements vm
        WHERE vm.vault_id = ? AND ${dateCondition}`,
        [vaultId]
      );

      return stats[0];
    } catch (error) {
      logger.error('Erro ao obter estatísticas do cofre:', error);
      throw error;
    }
  }
}

module.exports = new VaultService();
