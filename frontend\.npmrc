# Configuração npm para garantir instalação correta de dependências opcionais
# Especialmente importante para resolver problemas com @rollup/rollup-linux-x64-gnu na Vercel

# Instalar dependências opcionais (necessário para Rollup em diferentes plataformas)
optional=true

# Usar cache npm para acelerar builds
cache-max=86400000

# Configuração para ambientes CI/CD como Vercel
fund=false
audit=false

# Garantir que dependências de plataforma específica sejam instaladas
target_platform=linux
target_arch=x64

# Configuração para resolver problemas conhecidos do npm com dependências opcionais
# Referência: https://github.com/npm/cli/issues/4828
legacy-peer-deps=false
strict-peer-deps=false
