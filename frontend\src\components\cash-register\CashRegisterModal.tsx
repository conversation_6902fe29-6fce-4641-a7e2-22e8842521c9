import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Save, X } from 'lucide-react';
import { 
  CashRegister, 
  CreateCashRegisterRequest, 
  UpdateCashRegisterRequest,
  cashRegisterManagementService 
} from '@/services/cashRegisterManagementService';
import { branchService } from '@/services/branchService';

interface Branch {
  id: number;
  name: string;
  code: string;
}

interface CashRegisterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  cashRegister?: CashRegister | null;
  mode: 'create' | 'edit';
}

const CashRegisterModal: React.FC<CashRegisterModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  cashRegister,
  mode
}) => {
  const [formData, setFormData] = useState({
    register_number: '',
    description: '',
    branch_id: '',
    status: 'available' as const
  });
  
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingBranches, setIsLoadingBranches] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const { toast } = useToast();

  // Carregar balcões ao abrir modal
  useEffect(() => {
    if (isOpen) {
      loadBranches();
    }
  }, [isOpen]);

  // Preencher formulário quando em modo de edição
  useEffect(() => {
    if (isOpen && mode === 'edit' && cashRegister) {
      setFormData({
        register_number: cashRegister.register_number,
        description: cashRegister.description || '',
        branch_id: cashRegister.branch_id.toString(),
        status: cashRegister.status
      });
    } else if (isOpen && mode === 'create') {
      setFormData({
        register_number: '',
        description: '',
        branch_id: '',
        status: 'available'
      });
    }
    setErrors({});
  }, [isOpen, mode, cashRegister]);

  const loadBranches = async () => {
    try {
      setIsLoadingBranches(true);
      const branchesData = await branchService.getActiveBranches();
      setBranches(branchesData);
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao carregar balcões",
        variant: "destructive"
      });
    } finally {
      setIsLoadingBranches(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validar número do caixa
    const registerValidation = cashRegisterManagementService.validateRegisterNumber(formData.register_number);
    if (!registerValidation.isValid) {
      newErrors.register_number = registerValidation.error!;
    }

    // Validar descrição
    const descriptionValidation = cashRegisterManagementService.validateDescription(formData.description);
    if (!descriptionValidation.isValid) {
      newErrors.description = descriptionValidation.error!;
    }

    // Validar balcão
    if (!formData.branch_id) {
      newErrors.branch_id = 'Balcão é obrigatório';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);

      const requestData = {
        register_number: formData.register_number,
        description: formData.description || undefined,
        branch_id: parseInt(formData.branch_id),
        status: formData.status
      };

      if (mode === 'create') {
        await cashRegisterManagementService.createCashRegister(requestData as CreateCashRegisterRequest);
        toast({
          title: "Sucesso",
          description: "Caixa criado com sucesso"
        });
      } else {
        await cashRegisterManagementService.updateCashRegister(
          cashRegister!.id, 
          requestData as UpdateCashRegisterRequest
        );
        toast({
          title: "Sucesso",
          description: "Caixa atualizado com sucesso"
        });
      }

      onSuccess();
      onClose();
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || `Erro ao ${mode === 'create' ? 'criar' : 'atualizar'} caixa`,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando usuário começar a digitar
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {mode === 'create' ? 'Criar Novo Caixa' : 'Editar Caixa'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Número do Caixa */}
          <div className="space-y-2">
            <Label htmlFor="register_number">Número do Caixa *</Label>
            <Input
              id="register_number"
              value={formData.register_number}
              onChange={(e) => handleInputChange('register_number', e.target.value)}
              placeholder="Ex: CAIXA001"
              className={errors.register_number ? 'border-red-500' : ''}
            />
            {errors.register_number && (
              <p className="text-sm text-red-500">{errors.register_number}</p>
            )}
          </div>

          {/* Balcão */}
          <div className="space-y-2">
            <Label htmlFor="branch_id">Balcão *</Label>
            <Select
              value={formData.branch_id}
              onValueChange={(value) => handleInputChange('branch_id', value)}
            >
              <SelectTrigger className={errors.branch_id ? 'border-red-500' : ''}>
                <SelectValue placeholder={isLoadingBranches ? "Carregando..." : "Selecionar balcão"} />
              </SelectTrigger>
              <SelectContent>
                {branches.map((branch) => (
                  <SelectItem key={branch.id} value={branch.id.toString()}>
                    {branch.code} - {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.branch_id && (
              <p className="text-sm text-red-500">{errors.branch_id}</p>
            )}
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="available">Disponível</SelectItem>
                <SelectItem value="in_use">Em Uso</SelectItem>
                <SelectItem value="closed">Fechado</SelectItem>
                <SelectItem value="maintenance">Manutenção</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Descrição */}
          <div className="space-y-2">
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Descrição opcional do caixa..."
              rows={3}
              className={errors.description ? 'border-red-500' : ''}
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description}</p>
            )}
          </div>

          {/* Botões */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isLoading || isLoadingBranches}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {mode === 'create' ? 'Criar' : 'Atualizar'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CashRegisterModal;
