import React, { useState, useEffect } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { X, ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { menuItems } from '@/config/menuItems';
import { useAuth } from '@/contexts/AuthContext';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileMenu = ({ isOpen, onClose }: MobileMenuProps) => {
  const location = useLocation();
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const { user, hasPermission, hasRole } = useAuth();

  const toggleSubmenu = (itemId: string) => {
    setOpenSubmenu(openSubmenu === itemId ? null : itemId);
  };

  const handleNavClick = () => {
    onClose();
  };

  // Função para verificar se o usuário tem acesso a um item de menu
  // Implementa RBAC com diferentes níveis de acesso
  const hasAccessToMenuItem = (itemId: string): boolean => {
    switch (itemId) {
      case 'dashboard':
        return true; // Dashboard é acessível para todos
      case 'clientes':
        return hasPermission('clientes', 'read');
      case 'contas':
        return hasPermission('accounts', 'read');
      case 'caixa':
        return hasPermission('caixa', 'read');
      case 'tesouraria':
        return hasPermission('tesouraria', 'read');
      case 'transferencias':
        return hasPermission('transferencias', 'read');
      case 'cartoes':
        return hasPermission('cartoes', 'read');
      case 'cambios':
        return hasPermission('cambios', 'read');
      case 'seguros':
        return hasPermission('seguros', 'read');
      case 'sistema':
        return hasRole(['admin', 'gerente', 'tecnico']); // Sistema para admin, gerente e técnico
      case 'atm':
        return hasPermission('atm', 'read');
      default:
        return false;
    }
  };

  // Função para verificar se o usuário tem acesso a um submenu específico
  // Implementa regras específicas de RBAC incluindo perfil técnico
  const hasAccessToSubmenu = (path: string): boolean => {
    const userRole = user?.role;

    switch (path) {
      // Submenus de Caixa - Regras específicas por perfil
      case '/caixa/abertura-caixa':
        // Técnico: OCULTO (não tem acesso a dados financeiros)
        if (userRole === 'tecnico') return false;
        // Tesoureiro: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'tesoureiro') return false;
        return hasRole(['admin', 'gerente', 'caixa']);

      case '/caixa':
        // Técnico: OCULTO (não tem acesso a dados financeiros)
        if (userRole === 'tecnico') return false;
        // Tesoureiro: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'tesoureiro') return false;
        return hasRole(['admin', 'gerente', 'caixa']);

      case '/sistema/caixas-abertos':
        // Técnico: OCULTO (não tem acesso a dados financeiros)
        if (userRole === 'tecnico') return false;
        // Tesoureiro: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'tesoureiro') return false;
        // Admin/Gerente: VISÍVEL (para supervisão)
        // Caixa: OCULTO (não deve ver este submenu)
        return hasRole(['admin', 'gerente']);

      case '/sistema/caixas':
        // Técnico: OCULTO (não gerencia operações financeiras)
        if (userRole === 'tecnico') return false;
        return hasRole(['admin', 'gerente']);

      // Submenus de Tesouraria - Regras específicas por perfil
      case '/tesouraria/entrega-caixa':
        // Técnico: OCULTO (não tem acesso a operações financeiras)
        if (userRole === 'tecnico') return false;
        // Caixa: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'caixa') return false;
        return hasRole(['admin', 'gerente', 'tesoureiro']);

      case '/tesouraria/entrega-cofre':
        // Técnico: OCULTO (não tem acesso a operações financeiras)
        if (userRole === 'tecnico') return false;
        // Caixa: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'caixa') return false;
        return hasRole(['admin', 'gerente', 'tesoureiro']);

      case '/sistema/entrega-tesoureiro':
        // Técnico: OCULTO (não gerencia operações financeiras)
        if (userRole === 'tecnico') return false;
        return hasRole(['admin', 'gerente']);

      case '/sistema/entrega-balcao':
        // Técnico: OCULTO (não gerencia operações financeiras)
        if (userRole === 'tecnico') return false;
        return hasRole(['admin', 'gerente']);

      case '/tesouraria/carregamento-atm':
        // Técnico: OCULTO (não tem acesso a operações financeiras)
        if (userRole === 'tecnico') return false;
        // Caixa: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'caixa') return false;
        return hasRole(['admin', 'gerente', 'tesoureiro']);

      // Submenus de Sistema - Regras específicas para técnico
      case '/sistema/auditoria':
        // Técnico: VISÍVEL (pode acessar logs técnicos)
        return hasRole(['admin', 'gerente', 'tecnico']);

      case '/sistema/registar-usuario':
      case '/sistema/listar-usuario':
      case '/sistema/registar-role':
      case '/sistema/definir-tarefas':
      case '/sistema/data-sistema':
      case '/sistema/moeda-cambio':
        // Técnico: OCULTO (não gerencia configurações de negócio)
        if (userRole === 'tecnico') return false;
        return hasRole(['admin', 'gerente']);

      case '/sistema/registar-balcao':
        // Técnico: VISÍVEL (pode precisar para configurações técnicas)
        return hasRole(['admin', 'gerente', 'tecnico']);

      // Submenus de Clientes, Contas, Transferências, Cartões, Câmbios, Seguros
      case '/clientes/gestao-clientes':
      case '/clientes/abrir-conta-particular':
      case '/clientes/abrir-conta-empresa':
      case '/contas/gestao-contas':
      case '/clientes/aprovacao-contas':
      case '/transferencias':
      case '/cartoes':
      case '/cambios':
      case '/seguros':
        // Técnico: OCULTO (não tem acesso a dados financeiros/operacionais)
        if (userRole === 'tecnico') return false;
        return true; // Outros perfis seguem regras padrão

      // ATM - Técnico tem acesso para monitoramento técnico
      case '/atm':
        return hasRole(['admin', 'gerente', 'tesoureiro', 'tecnico']);

      // Por padrão, permitir acesso se o usuário tem acesso ao menu principal
      default:
        return true;
    }
  };

  // Filtrar itens de menu baseado nas permissões do usuário
  const filteredMenuItems = menuItems.filter(item => hasAccessToMenuItem(item.id)).map(item => {
    // Se o item tem submenu, filtrar os submenus baseado nas permissões
    if (item.submenu) {
      return {
        ...item,
        submenu: item.submenu.filter(subItem => hasAccessToSubmenu(subItem.path))
      };
    }
    return item;
  });

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('mobile-menu-open');
    } else {
      document.body.classList.remove('mobile-menu-open');
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('mobile-menu-open');
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black/50 z-40 md:hidden mobile-menu-overlay"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Mobile Menu */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-xl transform transition-transform duration-300 ease-in-out md:hidden mobile-menu-transition",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
        role="dialog"
        aria-modal="true"
        aria-label="Menu de navegação"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h1 className="text-xl font-bold bg-gradient-to-r from-twins-primary to-twins-secondary bg-clip-text text-transparent">
            twins_bank
          </h1>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-10 w-10 p-0 hover:bg-gray-100 mobile-touch-target"
            aria-label="Fechar menu"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto p-4 space-y-2">
          {filteredMenuItems.map((item) => {
            if (item.submenu) {
              return (
                <Collapsible
                  key={item.id}
                  open={openSubmenu === item.id}
                  onOpenChange={() => toggleSubmenu(item.id)}
                >
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      className="w-full justify-start h-12 px-4 text-left hover:bg-twins-accent/50 mobile-nav-item mobile-touch-target"
                    >
                      <item.icon className="h-5 w-5 mr-3" />
                      <span className="flex-1">{item.title}</span>
                      {openSubmenu === item.id ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="ml-8 space-y-1 mt-1">
                    {item.submenu.map((subItem) => {
                      const userRole = user?.role;
                      const isDisabled = (userRole === 'admin' || userRole === 'gerente') && (subItem.path === '/caixa/abertura-caixa' || subItem.path === '/caixa');
                      const isExactMatch = location.pathname === subItem.path;

                      return isDisabled ? (
                        // Elemento desabilitado (div) - não usa NavLink para evitar conflitos de roteamento
                        <div
                          key={subItem.path}
                          className={cn(
                            'flex items-center h-12 px-4 rounded-lg text-sm transition-all duration-200 mobile-nav-item mobile-touch-target',
                            'text-gray-400 cursor-not-allowed opacity-60'
                          )}
                          aria-disabled={true}
                        >
                          <subItem.icon className="h-4 w-4 mr-3" />
                          {subItem.title}
                        </div>
                      ) : (
                        // Elemento ativo (NavLink) - funcionalidade normal de navegação
                        <NavLink
                          key={subItem.path}
                          to={subItem.path}
                          end
                          onClick={handleNavClick}
                          className={({ isActive }) => cn(
                            'flex items-center h-12 px-4 rounded-lg text-sm transition-all duration-200 mobile-nav-item mobile-touch-target',
                            isActive
                              ? 'bg-gradient-to-r from-twins-primary to-twins-secondary text-white shadow-md'
                              : 'hover:bg-twins-accent/50 text-gray-700'
                          )}
                        >
                          <subItem.icon className="h-4 w-4 mr-3" />
                          {subItem.title}
                        </NavLink>
                      );
                    })}
                  </CollapsibleContent>
                </Collapsible>
              );
            }

            return (
              <NavLink
                key={item.id}
                to={item.path || '#'}
                end
                onClick={handleNavClick}
                className={({ isActive }) =>
                  cn(
                    "flex items-center h-12 px-4 rounded-lg text-sm transition-all duration-200 mobile-nav-item mobile-touch-target",
                    isActive
                      ? "bg-gradient-to-r from-twins-primary to-twins-secondary text-white shadow-md"
                      : item.disabled
                      ? "text-gray-400 cursor-not-allowed"
                      : "hover:bg-twins-accent/50 text-gray-700"
                  )
                }
              >
                <item.icon className="h-5 w-5 mr-3" />
                <span>{item.title}</span>
              </NavLink>
            );
          })}
        </nav>
      </div>
    </>
  );
};

export default MobileMenu;
