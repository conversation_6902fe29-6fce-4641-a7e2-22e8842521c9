import React, { useState, useEffect } from 'react';
import { X, User, Building2, Calendar, MapPin, Phone, Mail, FileText, Banknote, Clock, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Separator } from '../ui/separator';
import { approvalService, AccountApplication } from '../../services/approvalService';
import { useToast } from '../ui/use-toast';

interface ApplicationViewModalProps {
  applicationId: string;
  isOpen: boolean;
  onClose: () => void;
}

interface DetailedApplication extends AccountApplication {
  nif?: string;
  birth_date?: string;
  incorporation_date?: string;
  nationality?: string;
  profession?: string;
  street?: string;
  municipality?: string;
  province?: string;
  postal_code?: string;
  phone_personal?: string;
  phone_work?: string;
  email_personal?: string;
  email_work?: string;
  currency_id?: number;
  initial_deposit?: number;
  overdraft_limit?: number;
}

const ApplicationViewModal: React.FC<ApplicationViewModalProps> = ({
  applicationId,
  isOpen,
  onClose
}) => {
  const [application, setApplication] = useState<DetailedApplication | null>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen && applicationId) {
      loadApplicationDetails();
    }
  }, [isOpen, applicationId]);

  const loadApplicationDetails = async () => {
    try {
      setLoading(true);
      const details = await approvalService.getApplicationDetails(applicationId);
      setApplication(details as DetailedApplication);
    } catch (error: any) {
      console.error('Erro ao carregar detalhes:', error);
      toast({
        title: "Erro ao Carregar Detalhes",
        description: error.message || "Erro interno do servidor",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value?: number) => {
    if (!value) return '0,00 Kz';
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('pt-AO');
  };

  const formatDateTime = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('pt-AO');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pendente</Badge>;
      case 'approved':
        return <Badge variant="default" className="bg-green-100 text-green-800">Aprovado</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejeitado</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getAccountTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      'corrente': 'Conta Corrente',
      'salario': 'Conta Salário',
      'junior': 'Conta Júnior (2º titular)'
    };
    return types[type] || type;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            {application?.client_type === 'individual' ? (
              <User className="h-6 w-6 text-blue-600" />
            ) : (
              <Building2 className="h-6 w-6 text-purple-600" />
            )}
            <div>
              <h2 className="text-xl font-semibold">Detalhes da Solicitação</h2>
              <p className="text-sm text-gray-600">
                {application?.client_name || 'Carregando...'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {application && getStatusBadge(application.status)}
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Carregando detalhes...</p>
            </div>
          ) : application ? (
            <div className="space-y-6">
              {/* Informações da Solicitação */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Informações da Solicitação
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Tipo de Conta</label>
                    <p className="font-semibold">{getAccountTypeLabel(application.account_type)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Balcão</label>
                    <p className="font-semibold">{application.branch_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Depósito Inicial</label>
                    <p className="font-semibold">{formatCurrency(application.initial_deposit)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Limite de Descoberto</label>
                    <p className="font-semibold">{formatCurrency(application.overdraft_limit)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Data da Solicitação</label>
                    <p className="font-semibold flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {formatDateTime(application.created_at)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Solicitado por</label>
                    <p className="font-semibold">{application.requested_by}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Informações do Cliente */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {application.client_type === 'individual' ? (
                      <User className="h-5 w-5" />
                    ) : (
                      <Building2 className="h-5 w-5" />
                    )}
                    Informações do Cliente
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {application.client_type === 'individual' ? 'Nome Completo' : 'Razão Social'}
                    </label>
                    <p className="font-semibold">{application.client_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Documento</label>
                    <p className="font-semibold">{application.document_number}</p>
                  </div>
                  {application.nif && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">NIF</label>
                      <p className="font-semibold">{application.nif}</p>
                    </div>
                  )}
                  {application.client_type === 'individual' && application.birth_date && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Data de Nascimento</label>
                      <p className="font-semibold">{formatDate(application.birth_date)}</p>
                    </div>
                  )}
                  {application.client_type === 'company' && application.incorporation_date && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Data de Constituição</label>
                      <p className="font-semibold">{formatDate(application.incorporation_date)}</p>
                    </div>
                  )}
                  {application.nationality && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Nacionalidade</label>
                      <p className="font-semibold">{application.nationality}</p>
                    </div>
                  )}
                  {application.profession && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Profissão</label>
                      <p className="font-semibold">{application.profession}</p>
                    </div>
                  )}
                  {application.monthly_income && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Rendimento Mensal</label>
                      <p className="font-semibold">{formatCurrency(application.monthly_income)}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Endereço */}
              {(application.street || application.municipality || application.province) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Endereço
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {application.street && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Rua</label>
                        <p className="font-semibold">{application.street}</p>
                      </div>
                    )}
                    {application.municipality && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Município</label>
                        <p className="font-semibold">{application.municipality}</p>
                      </div>
                    )}
                    {application.province && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Província</label>
                        <p className="font-semibold">{application.province}</p>
                      </div>
                    )}
                    {application.postal_code && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Código Postal</label>
                        <p className="font-semibold">{application.postal_code}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Contactos */}
              {(application.phone_personal || application.email_personal || application.phone_work || application.email_work) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Phone className="h-5 w-5" />
                      Contactos
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {application.phone_personal && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Telefone Pessoal</label>
                        <p className="font-semibold flex items-center gap-2">
                          <Phone className="h-4 w-4" />
                          {application.phone_personal}
                        </p>
                      </div>
                    )}
                    {application.email_personal && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Email Pessoal</label>
                        <p className="font-semibold flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          {application.email_personal}
                        </p>
                      </div>
                    )}
                    {application.phone_work && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Telefone Profissional</label>
                        <p className="font-semibold flex items-center gap-2">
                          <Phone className="h-4 w-4" />
                          {application.phone_work}
                        </p>
                      </div>
                    )}
                    {application.email_work && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Email Profissional</label>
                        <p className="font-semibold flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          {application.email_work}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Status da Aprovação */}
              {(application.status === 'approved' || application.status === 'rejected') && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {application.status === 'approved' ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                      Status da Aprovação
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {application.status === 'approved' && (
                      <>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Aprovado por</label>
                          <p className="font-semibold">{application.approved_by || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Data de Aprovação</label>
                          <p className="font-semibold">{formatDateTime(application.approved_at)}</p>
                        </div>
                      </>
                    )}
                    {application.status === 'rejected' && (
                      <>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Rejeitado por</label>
                          <p className="font-semibold">{application.rejected_by || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Data de Rejeição</label>
                          <p className="font-semibold">{formatDateTime(application.rejected_at)}</p>
                        </div>
                        {application.rejection_reason && (
                          <div className="md:col-span-2">
                            <label className="text-sm font-medium text-gray-600">Motivo da Rejeição</label>
                            <p className="font-semibold text-red-600">{application.rejection_reason}</p>
                          </div>
                        )}
                      </>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Erro ao carregar detalhes da solicitação</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t bg-gray-50">
          <Button onClick={onClose}>
            Fechar
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ApplicationViewModal;
