import {
  LayoutDashboard,
  Users,
  Wallet,
  ArrowLeftRight,
  CreditCard,
  DollarSign,
  Shield,
  Settings,
  Banknote,
  UserCheck,
  Briefcase,
  AlertTriangle,
  Vault,
  Coins,
  Home,
  Building2,
  Search,
  Building,
  UserPlus,
  ClipboardList,
  Calendar,
  HandCoins,
  FolderOpen,
  DoorOpen,
  ShieldCheck,
  FileText,
  PiggyBank,
  Eye
} from 'lucide-react';

export interface MenuItem {
  id: string;
  title: string;
  icon: any;
  path?: string;
  disabled?: boolean;
  submenu?: {
    title: string;
    path: string;
    icon: any;
    disabled?: boolean;
  }[];
}

export const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: LayoutDashboard,
    path: '/'
  },
  {
    id: 'clientes',
    title: 'Clientes',
    icon: Users,
    submenu: [
      { title: 'Gestão de Clientes', path: '/clientes', icon: Users },
      { title: 'Abrir Conta Particular', path: '/clientes/abrir-conta-particular', icon: UserCheck },
      { title: 'Abrir Conta Empresa', path: '/clientes/abrir-conta-empresa', icon: Briefcase }
    ]
  },
  {
    id: 'contas',
    title: 'Con<PERSON>',
    icon: PiggyBank,
    submenu: [
      { title: 'Gestão de Contas', path: '/contas/gestao-contas', icon: Eye },
      { title: 'Aprovação de Contas', path: '/contas/aprovacao-contas', icon: ShieldCheck }
    ]
  },
  {
    id: 'caixa',
    title: 'Caixa',
    icon: Wallet,
    submenu: [
      { title: 'Abertura do Caixa', path: '/caixa/abertura-caixa', icon: DoorOpen },
      { title: 'Operações de Caixa', path: '/caixa', icon: Wallet },
      { title: 'Caixas Abertos', path: '/sistema/caixas-abertos', icon: FolderOpen },
      { title: 'Gestão de Caixas', path: '/sistema/caixas', icon: Wallet }
    ]
  },
  {
    id: 'tesouraria',
    title: 'Tesouraria',
    icon: Vault,
    submenu: [
      { title: 'Entrega ao Caixa', path: '/tesouraria/entrega-caixa', icon: Coins },
      { title: 'Entrega ao Cofre', path: '/tesouraria/entrega-cofre', icon: Vault },
      { title: 'Entrega ao Tesoureiro', path: '/sistema/entrega-tesoureiro', icon: HandCoins },
      { title: 'Entrega ao Balcão', path: '/sistema/entrega-balcao', icon: Building },
      { title: 'Carregamento do ATM', path: '/tesouraria/carregamento-atm', icon: Banknote }
    ]
  },
  {
    id: 'transferencias',
    title: 'Transferências',
    icon: ArrowLeftRight,
    submenu: [
      { title: 'Interna', path: '/transferencias/interna', icon: Home },
      { title: 'SPTR', path: '/transferencias/sptr', icon: Building2, disabled: true },
      { title: 'STC', path: '/transferencias/stc', icon: Building2, disabled: true },
      { title: 'Consultar Transferências', path: '/transferencias/consultar', icon: Search },
      { title: 'Movimentos Suspensos', path: '/transferencias/movimentos-suspensos', icon: AlertTriangle }
    ]
  },
  {
    id: 'cartoes',
    title: 'Cartões',
    icon: CreditCard,
    path: '/cartoes'
  },
  {
    id: 'cambios',
    title: 'Câmbios',
    icon: DollarSign,
    path: '/cambios'
  },
  {
    id: 'seguros',
    title: 'Seguros',
    icon: Shield,
    path: '/seguros',
    disabled: true
  },
  {
    id: 'sistema',
    title: 'Sistema',
    icon: Settings,
    submenu: [
      { title: 'Registar Balcão', path: '/sistema/registar-balcao', icon: Building2 },
      { title: 'Registar Usuário', path: '/sistema/registar-usuario', icon: UserPlus },
      { title: 'Listar Usuário', path: '/sistema/listar-usuario', icon: Users },
      { title: 'Gestão de Roles', path: '/sistema/registar-role', icon: ShieldCheck },
      { title: 'Saldo Inicial', path: '/sistema/saldo-inicial', icon: Vault },
      { title: 'Definir Tarefas', path: '/sistema/definir-tarefas', icon: ClipboardList },
      { title: 'Data Sistema', path: '/sistema/data-sistema', icon: Calendar },
      { title: 'Moeda / Câmbio', path: '/sistema/moeda-cambio', icon: DollarSign },
      { title: 'Auditoria e Logs', path: '/sistema/auditoria', icon: FileText }
    ]
  },
  {
    id: 'atm',
    title: 'ATM',
    icon: Banknote,
    path: '/atm'
  }
];
