import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useToast } from '@/hooks/use-toast';
import { clientService } from '@/services/clientService';
import { accountService } from '@/services/accountService';
import { approvalService, CreateApplicationRequest } from '@/services/approvalService';
import { branchService } from '@/services/branchService';
import { documentService } from '@/services/documentService';
import { IndividualClientForm } from '@/types/client';
import { CreateAccountRequest } from '@/services/accountService';
import { extractAndTranslateError } from '@/utils/errorTranslator';
import {
  Home,
  Contact,
  FileText,
  CreditCard,
  Image,
  AlertTriangle,
  Loader2,
  CheckCircle
} from 'lucide-react';

const AbrirContaParticular = () => {
  const [activeTab, setActiveTab] = useState('tipo-conta');
  const { toast } = useToast();

  // Estados para controle de validação e progresso
  const [tabsValidation, setTabsValidation] = useState({
    'tipo-conta': false,
    'dados-identificativos': false,
    'contactos': false,
    'habilitacao': false,
    'ficheiros': false,
    'segundo-titular': false
  });

  // Estados para controle de loading e submissão
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [branches, setBranches] = useState<any[]>([]);
  const [loadingBranches, setLoadingBranches] = useState(true);

  // Função para validar uma tab específica
  const validateTab = (tabName: string): boolean => {
    switch (tabName) {
      case 'tipo-conta':
        return !!(
          dadosConta.natureza &&
          dadosConta.tipoConta &&
          tipoContaSelecionado
        );

      case 'dados-identificativos':
        return !!(
          dadosIdentificativos.nome &&
          dadosIdentificativos.dataNascimento &&
          dadosIdentificativos.sexo &&
          dadosIdentificativos.nacionalidade &&
          dadosIdentificativos.tipoDocumento &&
          dadosIdentificativos.numeroIdentificacao &&
          dadosIdentificativos.localEmissao &&
          dadosIdentificativos.dataEmissao &&
          dadosIdentificativos.dataValidade &&
          dadosIdentificativos.nif &&
          dadosIdentificativos.estadoCivil &&
          dadosIdentificativos.provincia &&
          dadosIdentificativos.municipio
        );

      case 'contactos':
        return !!(
          contactos.telefonePersonal &&
          contactos.emailPersonal
        );

      case 'habilitacao':
        // Verificar se uma habilitação académica foi selecionada
        const temHabilitacaoAcademica = !!habilitacao.habilitacaoAcademica;

        // Verificar se uma situação profissional foi selecionada
        const temSituacaoProfissional = !!habilitacao.situacaoProfissional;

        // Se mostrar dados de emprego, verificar se os campos obrigatórios estão preenchidos
        const dadosEmpregoValidos = !habilitacao.mostrarDadosEmprego || (
          habilitacao.profissao &&
          habilitacao.funcao &&
          habilitacao.entidadePatronal &&
          habilitacao.cidade &&
          habilitacao.pais &&
          habilitacao.rendimentoCliente &&
          habilitacao.naturezaRendimento
        );

        return !!(temHabilitacaoAcademica && temSituacaoProfissional && dadosEmpregoValidos);

      case 'ficheiros':
        if (tipoContaSelecionado === 'individual') {
          // Verificar ficheiros obrigatórios básicos do primeiro titular
          const temImagemAssinatura = !!ficheiros.imagemAssinatura;
          const temDeclaracaoServico = !!ficheiros.imagemDeclaracaoServico;

          // Verificar ficheiro de documento baseado no tipo selecionado
          let temDocumentoIdentidade = false;
          if (dadosIdentificativos.tipoDocumento === 'BI') {
            temDocumentoIdentidade = !!ficheiros.imagemBI;
          } else if (dadosIdentificativos.tipoDocumento === 'Passaporte') {
            temDocumentoIdentidade = !!ficheiros.imagemPassaporte;
          } else if (dadosIdentificativos.tipoDocumento === 'C.Residente') {
            temDocumentoIdentidade = !!ficheiros.imagemCartaoResidente;
          }

          return temImagemAssinatura && temDocumentoIdentidade && temDeclaracaoServico;
        } else if (tipoContaSelecionado === 'conjunta') {
          // Para conta conjunta, validar ficheiros de ambos os titulares

          // Validar ficheiros do primeiro titular
          const temImagemAssinatura1 = !!ficheiros.imagemAssinatura;
          const temDeclaracaoServico1 = !!ficheiros.imagemDeclaracaoServico;
          let temDocumentoIdentidade1 = false;
          if (dadosIdentificativos.tipoDocumento === 'BI') {
            temDocumentoIdentidade1 = !!ficheiros.imagemBI;
          } else if (dadosIdentificativos.tipoDocumento === 'Passaporte') {
            temDocumentoIdentidade1 = !!ficheiros.imagemPassaporte;
          } else if (dadosIdentificativos.tipoDocumento === 'C.Residente') {
            temDocumentoIdentidade1 = !!ficheiros.imagemCartaoResidente;
          }

          // Validar ficheiros do segundo titular
          const temImagemAssinatura2 = !!ficheirosSegundoTitular.imagemAssinatura;
          const temDeclaracaoServico2 = !!ficheirosSegundoTitular.imagemDeclaracaoServico;
          let temDocumentoIdentidade2 = false;
          if (dadosSegundoTitular.tipoDocumento === 'BI') {
            temDocumentoIdentidade2 = !!ficheirosSegundoTitular.imagemBI;
          } else if (dadosSegundoTitular.tipoDocumento === 'Passaporte') {
            temDocumentoIdentidade2 = !!ficheirosSegundoTitular.imagemPassaporte;
          } else if (dadosSegundoTitular.tipoDocumento === 'C.Residente') {
            temDocumentoIdentidade2 = !!ficheirosSegundoTitular.imagemCartaoResidente;
          }

          const primeiroTitularCompleto = temImagemAssinatura1 && temDocumentoIdentidade1 && temDeclaracaoServico1;
          const segundoTitularCompleto = temImagemAssinatura2 && temDocumentoIdentidade2 && temDeclaracaoServico2;

          return primeiroTitularCompleto && segundoTitularCompleto;
        }
        return true;

      case 'segundo-titular':
        // Validação para segundo titular (se aplicável)
        if (dadosConta.tipoConta === 'junior' || tipoContaSelecionado === 'conjunta') {
          // Validar campos obrigatórios do segundo titular
          const camposObrigatorios = [
            dadosSegundoTitular.nome,
            dadosSegundoTitular.dataNascimento,
            dadosSegundoTitular.sexo,
            dadosSegundoTitular.nacionalidade,
            dadosSegundoTitular.tipoDocumento,
            dadosSegundoTitular.numeroIdentificacao,
            dadosSegundoTitular.localEmissao,
            dadosSegundoTitular.dataEmissao,
            dadosSegundoTitular.dataValidade,
            dadosSegundoTitular.nif,
            dadosSegundoTitular.estadoCivil,
            dadosSegundoTitular.exerceCargoPublico,
            dadosSegundoTitular.provincia,
            dadosSegundoTitular.municipio,
            // Contactos obrigatórios
            contactosSegundoTitular.telefonePersonal,
            contactosSegundoTitular.emailPersonal,
            // Habilitação obrigatória
            habilitacaoSegundoTitular.habilitacaoAcademica,
            habilitacaoSegundoTitular.situacaoProfissional
          ];

          // Validar campos condicionais de emprego se necessário
          if (habilitacaoSegundoTitular.mostrarDadosEmprego) {
            camposObrigatorios.push(
              habilitacaoSegundoTitular.profissao,
              habilitacaoSegundoTitular.funcao,
              habilitacaoSegundoTitular.entidadePatronal,
              habilitacaoSegundoTitular.cidade,
              habilitacaoSegundoTitular.pais,
              habilitacaoSegundoTitular.rendimentoCliente,
              habilitacaoSegundoTitular.naturezaRendimento
            );
          }

          return camposObrigatorios.every(campo => campo && campo.trim() !== '');
        }
        return true;

      default:
        return false;
    }
  };

  // Função para atualizar validação das tabs
  const updateTabValidation = () => {
    const newValidation = {
      'tipo-conta': validateTab('tipo-conta'),
      'dados-identificativos': validateTab('dados-identificativos'),
      'contactos': validateTab('contactos'),
      'habilitacao': validateTab('habilitacao'),
      'ficheiros': validateTab('ficheiros'),
      'segundo-titular': (dadosConta.tipoConta === 'junior' || tipoContaSelecionado === 'conjunta') ? validateTab('segundo-titular') : true
    };
    setTabsValidation(newValidation);
  };

  // Calcular progresso geral
  const calculateProgress = (): number => {
    const validTabs = Object.values(tabsValidation).filter(Boolean).length;
    const totalTabs = Object.keys(tabsValidation).length;
    return Math.round((validTabs / totalTabs) * 100);
  };

  // Função para obter classes CSS baseadas na validação do campo
  const getFieldClasses = (value: string, isRequired: boolean = true): string => {
    if (!isRequired) return '';
    if (!value) return 'border-red-300 focus:border-red-500 focus:ring-red-200';
    return 'border-green-300 focus:border-green-500 focus:ring-green-200';
  };

  // Função para lidar com upload de ficheiros
  const handleFileUpload = (fileType: keyof typeof ficheiros, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validar tipo de ficheiro
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Tipo de ficheiro inválido",
          description: "Apenas são permitidos ficheiros JPG, PNG ou PDF",
          variant: "destructive"
        });
        return;
      }

      // Validar tamanho do ficheiro (máximo 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        toast({
          title: "Ficheiro muito grande",
          description: "O tamanho máximo permitido é 5MB",
          variant: "destructive"
        });
        return;
      }

      setFicheiros(prev => ({
        ...prev,
        [fileType]: file
      }));

      toast({
        title: "Ficheiro carregado",
        description: `${file.name} foi carregado com sucesso`,
      });
    }
  };

  // Função para remover ficheiro
  const handleRemoveFile = (fileType: keyof typeof ficheiros) => {
    setFicheiros(prev => ({
      ...prev,
      [fileType]: null
    }));
  };

  // Função para lidar com upload de ficheiros do segundo titular
  const handleFileUploadSegundoTitular = (fileType: keyof typeof ficheirosSegundoTitular, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validar tipo de ficheiro
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Tipo de ficheiro inválido",
          description: "Apenas são permitidos ficheiros JPG, PNG ou PDF",
          variant: "destructive"
        });
        return;
      }

      // Validar tamanho do ficheiro (máximo 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB em bytes
      if (file.size > maxSize) {
        toast({
          title: "Ficheiro muito grande",
          description: "O ficheiro deve ter no máximo 5MB",
          variant: "destructive"
        });
        return;
      }

      setFicheirosSegundoTitular(prev => ({
        ...prev,
        [fileType]: file
      }));
    }
  };

  // Função para remover ficheiro do segundo titular
  const handleRemoveFileSegundoTitular = (fileType: keyof typeof ficheirosSegundoTitular) => {
    setFicheirosSegundoTitular(prev => ({
      ...prev,
      [fileType]: null
    }));
  };

  // Estados para os formulários
  const [dadosIdentificativos, setDadosIdentificativos] = useState({
    nome: '',
    dataNascimento: '',
    sexo: '',
    nacionalidade: '',
    naturalidade: '',
    tipoDocumento: '', // BI, Passaporte, C.Residente
    numeroIdentificacao: '',
    localEmissao: '',
    dataEmissao: '',
    dataValidade: '',
    nif: '',
    estadoCivil: '',
    separadoJudicialmente: false,
    regimeCasamento: '',
    exerceCargoPublico: '',
    qualCargoPublico: '',
    provincia: '',
    municipio: '',
    bairro: '',
    rua: '',
    mostrarRegimeCasamento: false,
    mostrarCargoPublico: false
  });

  const [contactos, setContactos] = useState({
    telefonePersonal: '',
    emailPersonal: '',
    telefoneProfissional: '',
    emailProfissional: ''
  });

  const [habilitacao, setHabilitacao] = useState({
    // Habilitações Académicas - agora usando string para radio button
    habilitacaoAcademica: '', // 'semEstudos', 'ensinoPrimario', 'ensinoSecundario', 'ensinoMedio', 'cursoSuperior'
    // Situação Profissional - agora usando string para radio button
    situacaoProfissional: '', // 'estudante', 'reformado', 'domestico', 'desempregado', 'trabalhadorContaOutrem', 'trabalhadorContaPropria'
    // Campos de emprego
    profissao: '',
    funcao: '',
    entidadePatronal: '',
    cidade: '',
    pais: '',
    rendimentoCliente: '',
    naturezaRendimento: '',
    mostrarDadosEmprego: false
  });

  const [dadosConta, setDadosConta] = useState({
    natureza: '',
    tipoConta: ''
  });

  const [ficheiros, setFicheiros] = useState({
    imagemAssinatura: null as File | null,
    imagemBI: null as File | null,
    imagemPassaporte: null as File | null,
    imagemCartaoResidente: null as File | null,
    imagemDeclaracaoServico: null as File | null
  });

  // Novos estados para fluxo de 1 ou 2 titulares
  const [tipoContaSelecionado, setTipoContaSelecionado] = useState<'individual' | 'conjunta' | ''>('');

  const [dadosSegundoTitular, setDadosSegundoTitular] = useState({
    nome: '',
    dataNascimento: '',
    sexo: '',
    nacionalidade: '',
    naturalidade: '',
    tipoDocumento: '',
    numeroIdentificacao: '',
    localEmissao: '',
    dataEmissao: '',
    dataValidade: '',
    nif: '',
    estadoCivil: '',
    separadoJudicialmente: false,
    regimeCasamento: '',
    exerceCargoPublico: '',
    qualCargoPublico: '',
    provincia: '',
    municipio: '',
    bairro: '',
    rua: '',
    mostrarRegimeCasamento: false,
    mostrarCargoPublico: false
  });

  // Estados para contactos do segundo titular
  const [contactosSegundoTitular, setContactosSegundoTitular] = useState({
    telefonePersonal: '',
    emailPersonal: '',
    telefoneProfissional: '',
    emailProfissional: ''
  });

  // Estados para habilitação do segundo titular
  const [habilitacaoSegundoTitular, setHabilitacaoSegundoTitular] = useState({
    habilitacaoAcademica: '',
    situacaoProfissional: '',
    profissao: '',
    funcao: '',
    entidadePatronal: '',
    cidade: '',
    pais: '',
    rendimentoCliente: '',
    naturezaRendimento: '',
    mostrarDadosEmprego: false
  });

  const [ficheirosSegundoTitular, setFicheirosSegundoTitular] = useState({
    imagemAssinatura: null,
    imagemBI: null,
    imagemPassaporte: null,
    imagemCartaoResidente: null,
    imagemDeclaracaoServico: null
  });

  // Atualizar validação sempre que os dados mudarem
  useEffect(() => {
    updateTabValidation();
  }, [dadosIdentificativos, contactos, habilitacao, dadosConta, ficheiros, tipoContaSelecionado, dadosSegundoTitular, contactosSegundoTitular, habilitacaoSegundoTitular]);

  // Funções de controle para lógica condicional
  const handleEstadoCivilChange = (value: string) => {
    const mostrarRegime = value === 'Casado(a)' || value === 'Separado(a) Judicialmente';
    setDadosIdentificativos(prev => ({
      ...prev,
      estadoCivil: value,
      mostrarRegimeCasamento: mostrarRegime,
      regimeCasamento: mostrarRegime ? prev.regimeCasamento : ''
    }));
  };

  const handleCargoPublicoChange = (value: string) => {
    const mostrarCargo = value === 'Sim';
    setDadosIdentificativos(prev => ({
      ...prev,
      exerceCargoPublico: value,
      mostrarCargoPublico: mostrarCargo,
      qualCargoPublico: mostrarCargo ? prev.qualCargoPublico : ''
    }));
  };

  // Função para lidar com mudança de habilitação académica
  const handleHabilitacaoAcademicaChange = (valor: string) => {
    setHabilitacao(prev => ({
      ...prev,
      habilitacaoAcademica: valor
    }));
  };

  // Função para lidar com mudança de situação profissional
  const handleSituacaoProfissionalChange = (valor: string) => {
    const mostrarDados = valor === 'trabalhadorContaOutrem' || valor === 'trabalhadorContaPropria';

    setHabilitacao(prev => ({
      ...prev,
      situacaoProfissional: valor,
      mostrarDadosEmprego: mostrarDados,
      // Limpar campos de emprego se não for mais necessário
      profissao: mostrarDados ? prev.profissao : '',
      funcao: mostrarDados ? prev.funcao : '',
      entidadePatronal: mostrarDados ? prev.entidadePatronal : '',
      cidade: mostrarDados ? prev.cidade : '',
      pais: mostrarDados ? prev.pais : '',
      rendimentoCliente: mostrarDados ? prev.rendimentoCliente : '',
      naturezaRendimento: mostrarDados ? prev.naturezaRendimento : ''
    }));
  };

  // Funções para o segundo titular
  const handleHabilitacaoAcademicaChangeSegundoTitular = (valor: string) => {
    setHabilitacaoSegundoTitular(prev => ({
      ...prev,
      habilitacaoAcademica: valor
    }));
  };

  const handleSituacaoProfissionalChangeSegundoTitular = (valor: string) => {
    const mostrarDados = valor === 'trabalhadorContaOutrem' || valor === 'trabalhadorContaPropria';

    setHabilitacaoSegundoTitular(prev => ({
      ...prev,
      situacaoProfissional: valor,
      mostrarDadosEmprego: mostrarDados,
      // Limpar campos de emprego se não for mais necessário
      profissao: mostrarDados ? prev.profissao : '',
      funcao: mostrarDados ? prev.funcao : '',
      entidadePatronal: mostrarDados ? prev.entidadePatronal : '',
      cidade: mostrarDados ? prev.cidade : '',
      pais: mostrarDados ? prev.pais : '',
      rendimentoCliente: mostrarDados ? prev.rendimentoCliente : '',
      naturezaRendimento: mostrarDados ? prev.naturezaRendimento : ''
    }));
  };

  // Carregar balcões disponíveis
  useEffect(() => {
    const loadBranches = async () => {
      try {
        setLoadingBranches(true);
        const branchList = await branchService.listBranches({
          page: 1,
          limit: 100,
          active: true
        });
        setBranches(branchList.branches);
      } catch (error) {
        console.error('Erro ao carregar balcões:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar lista de balcões",
          variant: "destructive"
        });
      } finally {
        setLoadingBranches(false);
      }
    };

    loadBranches();
  }, [toast]);

  // Função para validar todos os dados obrigatórios
  const validateAllRequiredFields = (): { isValid: boolean; missingTab?: string; missingFields: string[] } => {
    const missingFields: string[] = [];
    let missingTab = '';

    // Validar Dados Identificativos
    if (!dadosIdentificativos.nome) missingFields.push('Nome completo');
    if (!dadosIdentificativos.dataNascimento) missingFields.push('Data de nascimento');
    if (!dadosIdentificativos.sexo) missingFields.push('Sexo');
    if (!dadosIdentificativos.nacionalidade) missingFields.push('Nacionalidade');
    if (!dadosIdentificativos.tipoDocumento) missingFields.push('Tipo de documento');
    if (!dadosIdentificativos.numeroIdentificacao) missingFields.push('Número de identificação');
    if (!dadosIdentificativos.localEmissao) missingFields.push('Local de emissão');
    if (!dadosIdentificativos.dataEmissao) missingFields.push('Data de emissão');
    if (!dadosIdentificativos.dataValidade) missingFields.push('Data de validade');
    if (!dadosIdentificativos.nif) missingFields.push('NIF');
    if (!dadosIdentificativos.estadoCivil) missingFields.push('Estado civil');
    if (!dadosIdentificativos.provincia) missingFields.push('Província');
    if (!dadosIdentificativos.municipio) missingFields.push('Município');

    if (missingFields.length > 0 && !missingTab) {
      missingTab = 'dados-identificativos';
    }

    // Validar Dados da Conta
    if (!dadosConta.natureza) missingFields.push('Natureza da conta');
    if (!dadosConta.tipoConta) missingFields.push('Tipo de conta');
    if (!tipoContaSelecionado) missingFields.push('Tipo de conta selecionado');

    if ((missingFields.includes('Natureza da conta') || missingFields.includes('Tipo de conta') || missingFields.includes('Tipo de conta selecionado')) && !missingTab) {
      missingTab = 'dados-conta';
    }

    // Validar Ficheiros Obrigatórios
    if (tipoContaSelecionado === 'individual') {
      if (!ficheiros.imagemAssinatura) missingFields.push('Imagem da assinatura');
      if (dadosIdentificativos.tipoDocumento === 'BI' && !ficheiros.imagemBI) missingFields.push('Imagem do BI');
      if (dadosIdentificativos.tipoDocumento === 'Passaporte' && !ficheiros.imagemPassaporte) missingFields.push('Imagem do passaporte');
      if (!ficheiros.imagemDeclaracaoServico) missingFields.push('Imagem da declaração de serviço');
    } else if (tipoContaSelecionado === 'conjunta') {
      // Para conta conjunta, validar ficheiros de ambos os titulares

      // Validar ficheiros do 1º titular
      if (!ficheiros.imagemAssinatura) missingFields.push('Assinatura do 1º titular');
      if (dadosIdentificativos.tipoDocumento === 'BI' && !ficheiros.imagemBI) missingFields.push('BI do 1º titular');
      if (dadosIdentificativos.tipoDocumento === 'Passaporte' && !ficheiros.imagemPassaporte) missingFields.push('Passaporte do 1º titular');
      if (dadosIdentificativos.tipoDocumento === 'C.Residente' && !ficheiros.imagemCartaoResidente) missingFields.push('Cartão de Residente do 1º titular');
      if (!ficheiros.imagemDeclaracaoServico) missingFields.push('Declaração de serviço do 1º titular');

      // Validar ficheiros do 2º titular
      if (!ficheirosSegundoTitular.imagemAssinatura) missingFields.push('Assinatura do 2º titular');
      if (dadosSegundoTitular.tipoDocumento === 'BI' && !ficheirosSegundoTitular.imagemBI) missingFields.push('BI do 2º titular');
      if (dadosSegundoTitular.tipoDocumento === 'Passaporte' && !ficheirosSegundoTitular.imagemPassaporte) missingFields.push('Passaporte do 2º titular');
      if (dadosSegundoTitular.tipoDocumento === 'C.Residente' && !ficheirosSegundoTitular.imagemCartaoResidente) missingFields.push('Cartão de Residente do 2º titular');
      if (!ficheirosSegundoTitular.imagemDeclaracaoServico) missingFields.push('Declaração de serviço do 2º titular');

      // Validar dados obrigatórios do 2º titular
      if (!dadosSegundoTitular.nome) missingFields.push('Nome do 2º titular');
      if (!dadosSegundoTitular.dataNascimento) missingFields.push('Data de nascimento do 2º titular');
      if (!dadosSegundoTitular.sexo) missingFields.push('Sexo do 2º titular');
      if (!dadosSegundoTitular.nacionalidade) missingFields.push('Nacionalidade do 2º titular');
      if (!dadosSegundoTitular.tipoDocumento) missingFields.push('Tipo de documento do 2º titular');
      if (!dadosSegundoTitular.numeroIdentificacao) missingFields.push('Número de identificação do 2º titular');
      if (!dadosSegundoTitular.localEmissao) missingFields.push('Local de emissão do 2º titular');
      if (!dadosSegundoTitular.dataEmissao) missingFields.push('Data de emissão do 2º titular');
      if (!dadosSegundoTitular.dataValidade) missingFields.push('Data de validade do 2º titular');
      if (!dadosSegundoTitular.nif) missingFields.push('NIF do 2º titular');
      if (!dadosSegundoTitular.estadoCivil) missingFields.push('Estado civil do 2º titular');
      if (!dadosSegundoTitular.exerceCargoPublico) missingFields.push('Exercer cargo público do 2º titular');
      if (!dadosSegundoTitular.provincia) missingFields.push('Província do 2º titular');
      if (!dadosSegundoTitular.municipio) missingFields.push('Município do 2º titular');
    }

    if ((missingFields.includes('Imagem da assinatura') || missingFields.includes('Imagem do BI') || missingFields.includes('Imagem da declaração de serviço') || missingFields.includes('Assinatura do 1º titular') || missingFields.includes('BI do 1º titular') || missingFields.includes('Declaração de serviço do 1º titular')) && !missingTab) {
      missingTab = 'ficheiros';
    }

    return {
      isValid: missingFields.length === 0,
      missingTab,
      missingFields
    };
  };

  const handleTerminarRegistro = async () => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);

      // Validar todos os campos obrigatórios
      const validation = validateAllRequiredFields();

      if (!validation.isValid) {
        // Navegar para a tab com campos em falta
        if (validation.missingTab) {
          setActiveTab(validation.missingTab);
        }

        toast({
          title: "Dados Incompletos",
          description: `Campos obrigatórios em falta: ${validation.missingFields.join(', ')}`,
          variant: "destructive"
        });
        return;
      }

      // Preparar dados do cliente individual
      const clientData: IndividualClientForm = {
        full_name: dadosIdentificativos.nome,
        document_type: dadosIdentificativos.tipoDocumento as 'BI' | 'Passaporte' | 'Cedula',
        document_number: dadosIdentificativos.numeroIdentificacao,
        nif: dadosIdentificativos.nif || null,
        birth_date: dadosIdentificativos.dataNascimento,
        nationality: dadosIdentificativos.nacionalidade,
        gender: dadosIdentificativos.sexo as 'M' | 'F',
        marital_status: dadosIdentificativos.estadoCivil === 'Solteiro(a)' ? 'Solteiro' :
                       dadosIdentificativos.estadoCivil === 'Casado(a)' ? 'Casado' :
                       dadosIdentificativos.estadoCivil === 'Divorciado(a)' ? 'Divorciado' :
                       dadosIdentificativos.estadoCivil === 'Viúvo(a)' ? 'Viúvo' :
                       dadosIdentificativos.estadoCivil === 'União de Facto' ? 'União de Facto' :
                       'Solteiro', // default
        profession: habilitacao.profissao || 'Não especificado',
        monthly_income: habilitacao.rendimentoCliente ? parseFloat(habilitacao.rendimentoCliente) : 1,
        branch_id: branches.length > 0 ? branches[0].id : 1, // Usar primeiro balcão disponível
        address: {
          street: `${dadosIdentificativos.rua}, ${dadosIdentificativos.bairro}`,
          municipality: dadosIdentificativos.municipio,
          province: dadosIdentificativos.provincia,
          postal_code: '1000' // Código postal padrão para Luanda
        },
        contacts: {
          phone_personal: contactos.telefonePersonal || '000000000',
          email_personal: contactos.emailPersonal || '<EMAIL>'
        }
      };

      // Criar cliente
      const createdClient = await clientService.createIndividualClient(clientData);

      // Upload de documentos para Supabase
      const documentsToUpload: Record<string, File> = {};

      // Adicionar documentos obrigatórios
      if (ficheiros.imagemAssinatura) {
        documentsToUpload.imagemAssinatura = ficheiros.imagemAssinatura;
      }
      if (ficheiros.imagemDeclaracaoServico) {
        documentsToUpload.imagemDeclaracaoServico = ficheiros.imagemDeclaracaoServico;
      }

      // Adicionar documento de identificação baseado no tipo selecionado
      if (dadosIdentificativos.tipoDocumento === 'BI' && ficheiros.imagemBI) {
        documentsToUpload.imagemBI = ficheiros.imagemBI;
      } else if (dadosIdentificativos.tipoDocumento === 'Passaporte' && ficheiros.imagemPassaporte) {
        documentsToUpload.imagemPassaporte = ficheiros.imagemPassaporte;
      } else if (dadosIdentificativos.tipoDocumento === 'Cartão de Residente' && ficheiros.imagemCartaoResidente) {
        documentsToUpload.imagemCartaoResidente = ficheiros.imagemCartaoResidente;
      }

      // Fazer upload dos documentos
      if (Object.keys(documentsToUpload).length > 0) {
        try {
          await documentService.uploadClientDocuments(
            createdClient.id,
            documentsToUpload,
            'individual'
          );
        } catch (uploadError: any) {
          // Se o upload falhar, eliminar o cliente criado e mostrar erro
          try {
            await clientService.deleteClient(createdClient.id);
          } catch (deleteError) {
            console.error('Erro ao eliminar cliente após falha no upload:', deleteError);
          }

          throw new Error(`Erro no upload de documentos: ${uploadError.message}. A conta não foi criada.`);
        }
      }

      // Mapear tipo de conta selecionado para valor do backend
      let backendAccountType = 'corrente'; // Padrão para Conta Particular/Singular
      if (dadosConta.tipoConta === 'salario') {
        backendAccountType = 'salario';
      } else if (dadosConta.tipoConta === 'junior') {
        backendAccountType = 'junior';
      }

      // Preparar dados da solicitação de conta
      const applicationData: CreateApplicationRequest = {
        client_id: createdClient.id,
        account_type: backendAccountType, // Usa o valor correto do backend
        currency_id: 1, // AOA
        initial_deposit: 0,
        branch_id: branches.length > 0 ? branches[0].id : 1,
        overdraft_limit: backendAccountType === 'corrente' ? 100000 : 0 // Limite apenas para contas particulares
      };

      // Criar solicitação de abertura de conta
      const createdApplication = await approvalService.createIndividualApplication(applicationData);

      toast({
        title: "Solicitação Criada com Sucesso!",
        description: `Solicitação de abertura de conta criada para ${createdClient.full_name}. Documentos carregados com sucesso. Aguardando aprovação.`,
      });

      // Limpar formulário após sucesso
      setDadosIdentificativos({
        nome: '', dataNascimento: '', sexo: '', nacionalidade: '', naturalidade: '',
        tipoDocumento: '', numeroIdentificacao: '', localEmissao: '', dataEmissao: '',
        dataValidade: '', nif: '', estadoCivil: '', separadoJudicialmente: false,
        regimeCasamento: '', exerceCargoPublico: '', qualCargoPublico: '',
        provincia: '', municipio: '', bairro: '', rua: '',
        mostrarRegimeCasamento: false, mostrarCargoPublico: false
      });
      setContactos({ telefonePersonal: '', emailPersonal: '', telefoneProfissional: '', emailProfissional: '' });
      setHabilitacao({
        habilitacaoAcademica: '',
        situacaoProfissional: '',
        profissao: '', funcao: '', entidadePatronal: '', cidade: '', pais: '',
        rendimentoCliente: '', naturezaRendimento: '', mostrarDadosEmprego: false
      });
      setFicheiros({
        imagemAssinatura: null,
        imagemBI: null,
        imagemPassaporte: null,
        imagemCartaoResidente: null,
        imagemDeclaracaoServico: null
      });
      setTipoContaSelecionado('');
      setActiveTab('dados-identificativos');

    } catch (error: any) {
      console.error('Erro ao criar conta:', error);
      const translatedError = extractAndTranslateError(error);
      toast({
        title: "Erro ao Criar Conta",
        description: translatedError,
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 content-container">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Abertura de Conta Particular</h1>
        <p className="text-gray-600 dark:text-gray-400">Registar Cliente</p>
      </div>

      {/* Seleção inicial do tipo de conta */}
      {!tipoContaSelecionado && (
        <Card>
          <CardContent className="p-8">
            <div className="text-center space-y-6">
              <h2 className="text-2xl font-semibold">Tipo de Conta</h2>
              <p className="text-gray-600">Selecione o tipo de conta que deseja abrir:</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                <Button
                  variant="outline"
                  className="h-32 flex flex-col items-center justify-center space-y-3 hover:bg-blue-50 hover:border-blue-300"
                  onClick={() => setTipoContaSelecionado('individual')}
                >
                  <div className="text-4xl">👤</div>
                  <div className="text-lg font-semibold">Conta Individual</div>
                  <div className="text-sm text-gray-500">Para uma pessoa</div>
                </Button>

                <Button
                  variant="outline"
                  className="h-32 flex flex-col items-center justify-center space-y-3 hover:bg-green-50 hover:border-green-300"
                  onClick={() => setTipoContaSelecionado('conjunta')}
                >
                  <div className="text-4xl">👥</div>
                  <div className="text-lg font-semibold">Conta Conjunta</div>
                  <div className="text-sm text-gray-500">Para 2 titulares</div>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Formulário principal - só aparece após seleção do tipo */}
      {tipoContaSelecionado && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">Tipo selecionado:</span>
                <span className="font-semibold">
                  {tipoContaSelecionado === 'individual' ? 'Conta Individual' : 'Conta Conjunta (2 Titulares)'}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setTipoContaSelecionado('')}
              >
                Alterar Tipo
              </Button>
            </div>

            {/* Barra de Progresso */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Progresso do Formulário</span>
                <span className="text-sm text-gray-500">{calculateProgress()}% completo</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-in-out"
                  style={{ width: `${calculateProgress()}%` }}
                ></div>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full overflow-x-auto scrollbar-hide whitespace-nowrap flex">
              <TabsTrigger
                value="tipo-conta"
                className={`flex items-center space-x-2 text-xs px-4 py-2 min-w-fit ${
                  tabsValidation['tipo-conta'] ? 'bg-green-50 border-green-200' : ''
                }`}
              >
                {tabsValidation['tipo-conta'] ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <CreditCard className="h-4 w-4" />
                )}
                <span className="hidden sm:inline">Tipo de Conta</span>
              </TabsTrigger>
              <TabsTrigger
                value="dados-identificativos"
                disabled={!tabsValidation['tipo-conta']}
                className={`flex items-center space-x-2 text-xs px-4 py-2 min-w-fit ${
                  tabsValidation['dados-identificativos'] ? 'bg-green-50 border-green-200' : ''
                } ${!tabsValidation['tipo-conta'] ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {tabsValidation['dados-identificativos'] ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <Home className="h-4 w-4" />
                )}
                <span className="hidden sm:inline">Dados Identificativos</span>
              </TabsTrigger>
              <TabsTrigger
                value="contactos"
                disabled={!tabsValidation['tipo-conta']}
                className={`flex items-center space-x-2 text-xs px-4 py-2 min-w-fit ${
                  tabsValidation['contactos'] ? 'bg-green-50 border-green-200' : ''
                } ${!tabsValidation['tipo-conta'] ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {tabsValidation['contactos'] ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <Contact className="h-4 w-4" />
                )}
                <span className="hidden sm:inline">Contactos</span>
              </TabsTrigger>
              <TabsTrigger
                value="habilitacao"
                disabled={!tabsValidation['tipo-conta']}
                className={`flex items-center space-x-2 text-xs px-4 py-2 min-w-fit ${
                  tabsValidation['habilitacao'] ? 'bg-green-50 border-green-200' : ''
                } ${!tabsValidation['tipo-conta'] ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {tabsValidation['habilitacao'] ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <FileText className="h-4 w-4" />
                )}
                <span className="hidden sm:inline">Habilitação e Dados Profissionais</span>
              </TabsTrigger>
              <TabsTrigger
                value="ficheiros"
                disabled={!tabsValidation['tipo-conta']}
                className={`flex items-center space-x-2 text-xs px-4 py-2 min-w-fit ${
                  tabsValidation['ficheiros'] ? 'bg-green-50 border-green-200' : ''
                } ${!tabsValidation['tipo-conta'] ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {tabsValidation['ficheiros'] ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <Image className="h-4 w-4" />
                )}
                <span className="hidden sm:inline">Ficheiros</span>
              </TabsTrigger>
              {(dadosConta.tipoConta === 'junior' || tipoContaSelecionado === 'conjunta') && (
                <TabsTrigger
                  value="segundo-titular"
                  disabled={!tabsValidation['tipo-conta']}
                  className={`flex items-center space-x-2 text-xs px-4 py-2 min-w-fit ${
                    tabsValidation['segundo-titular'] ? 'bg-green-50 border-green-200' : ''
                  } ${!tabsValidation['tipo-conta'] ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {tabsValidation['segundo-titular'] ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4" />
                  )}
                  <span className="hidden sm:inline">2º Titular</span>
                </TabsTrigger>
              )}
            </TabsList>

            {/* Aba Tipo de Conta */}
            <TabsContent value="tipo-conta" className="mt-6 space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Configuração da Conta</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="natureza">Natureza da Conta <span className="text-red-500">*</span></Label>
                    <Select value={dadosConta.natureza} onValueChange={(value) => setDadosConta(prev => ({ ...prev, natureza: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione a natureza" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="kwanza">Kwanza - Kz</SelectItem>
                        <SelectItem value="euro">Euro - EUR</SelectItem>
                        <SelectItem value="dolar">Dólar - USD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="tipo-conta">Tipo de Conta <span className="text-red-500">*</span></Label>
                    <Select value={dadosConta.tipoConta} onValueChange={(value) => setDadosConta(prev => ({ ...prev, tipoConta: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="corrente">Conta Particular/Singular</SelectItem>
                        <SelectItem value="salario">Conta Salário</SelectItem>
                        <SelectItem value="junior">Conta Júnior (2º titular)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Aba Contactos */}
            <TabsContent value="contactos" className="mt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Telefones Pessoais */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="telefone-personal">Telefone Pessoal <span className="text-red-500">*</span></Label>
                    <Input
                      id="telefone-personal"
                      value={contactos.telefonePersonal}
                      onChange={(e) => setContactos(prev => ({ ...prev, telefonePersonal: e.target.value }))}
                      placeholder="Telefone pessoal"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email-personal">Email Pessoal <span className="text-red-500">*</span></Label>
                    <Input
                      id="email-personal"
                      type="email"
                      value={contactos.emailPersonal}
                      onChange={(e) => setContactos(prev => ({ ...prev, emailPersonal: e.target.value }))}
                      placeholder="Email pessoal"
                    />
                  </div>
                </div>

                {/* Telefones Profissionais */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="telefone-profissional">Telefone Profissional</Label>
                    <Input
                      id="telefone-profissional"
                      value={contactos.telefoneProfissional}
                      onChange={(e) => setContactos(prev => ({ ...prev, telefoneProfissional: e.target.value }))}
                      placeholder="Telefone profissional"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email-profissional">Email Profissional</Label>
                    <Input
                      id="email-profissional"
                      type="email"
                      value={contactos.emailProfissional}
                      onChange={(e) => setContactos(prev => ({ ...prev, emailProfissional: e.target.value }))}
                      placeholder="Email profissional"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Aba Dados da Conta */}
            <TabsContent value="dados-conta" className="mt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="natureza">Natureza <span className="text-red-500">*</span></Label>
                    <Select value={dadosConta.natureza} onValueChange={(value) => setDadosConta(prev => ({ ...prev, natureza: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleciona Natureza" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="kwanza">Kwanza - Kz</SelectItem>
                        <SelectItem value="euro">Euro - EUR</SelectItem>
                        <SelectItem value="dolar">Dólar - USD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="tipo-conta">Tipo Conta <span className="text-red-500">*</span></Label>
                    <Select value={dadosConta.tipoConta} onValueChange={(value) => setDadosConta(prev => ({ ...prev, tipoConta: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleciona o Tipo de Conta" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="corrente">Conta Particular/Singular</SelectItem>
                        <SelectItem value="salario">Conta Salário</SelectItem>
                        <SelectItem value="junior">Conta Júnior (2º titular)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Aba Dados Identificativos */}
            <TabsContent value="dados-identificativos" className="mt-6 space-y-8">

              {/* Secção 1: Identificação Pessoal */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Identificação Pessoal</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="nome">Nome <span className="text-red-500">*</span></Label>
                    <Input
                      id="nome"
                      value={dadosIdentificativos.nome}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, nome: e.target.value }))}
                      placeholder="Nome completo"
                      className={getFieldClasses(dadosIdentificativos.nome)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="data-nascimento">Data de Nascimento <span className="text-red-500">*</span></Label>
                    <Input
                      id="data-nascimento"
                      type="date"
                      value={dadosIdentificativos.dataNascimento}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, dataNascimento: e.target.value }))}
                      className={getFieldClasses(dadosIdentificativos.dataNascimento)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Sexo <span className="text-red-500">*</span></Label>
                    <RadioGroup
                      value={dadosIdentificativos.sexo}
                      onValueChange={(value) => setDadosIdentificativos(prev => ({ ...prev, sexo: value }))}
                      className="flex items-center space-x-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="M" id="sexo-m" />
                        <Label htmlFor="sexo-m">Masculino</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="F" id="sexo-f" />
                        <Label htmlFor="sexo-f">Feminino</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="nacionalidade">Nacionalidade <span className="text-red-500">*</span></Label>
                    <Input
                      id="nacionalidade"
                      value={dadosIdentificativos.nacionalidade}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, nacionalidade: e.target.value }))}
                      placeholder="Nacionalidade"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="naturalidade">Naturalidade</Label>
                    <Input
                      id="naturalidade"
                      value={dadosIdentificativos.naturalidade}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, naturalidade: e.target.value }))}
                      placeholder="Naturalidade"
                    />
                  </div>
                </div>
              </div>

              {/* Secção 2: Documento de Identificação */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">Documento de Identificação</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Tipo de Documento <span className="text-red-500">*</span></Label>
                    <RadioGroup
                      value={dadosIdentificativos.tipoDocumento}
                      onValueChange={(value) => setDadosIdentificativos(prev => ({ ...prev, tipoDocumento: value }))}
                      className="flex items-center space-x-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="BI" id="doc-bi" />
                        <Label htmlFor="doc-bi">Bilhete de Identidade</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Passaporte" id="doc-passaporte" />
                        <Label htmlFor="doc-passaporte">Passaporte</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="C.Residente" id="doc-residente" />
                        <Label htmlFor="doc-residente">Cartão de Residente</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="numero-identificacao">Nº de Identificação <span className="text-red-500">*</span></Label>
                    <Input
                      id="numero-identificacao"
                      value={dadosIdentificativos.numeroIdentificacao}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, numeroIdentificacao: e.target.value }))}
                      placeholder="Número de identificação"
                      disabled={!dadosIdentificativos.tipoDocumento}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="local-emissao">Local de Emissão <span className="text-red-500">*</span></Label>
                    <Input
                      id="local-emissao"
                      value={dadosIdentificativos.localEmissao}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, localEmissao: e.target.value }))}
                      placeholder="Local de emissão"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="data-emissao">Data de Emissão <span className="text-red-500">*</span></Label>
                    <Input
                      id="data-emissao"
                      type="date"
                      value={dadosIdentificativos.dataEmissao}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, dataEmissao: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="data-validade">Data de Validade <span className="text-red-500">*</span></Label>
                    <Input
                      id="data-validade"
                      type="date"
                      value={dadosIdentificativos.dataValidade}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, dataValidade: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="nif">NIF <span className="text-red-500">*</span></Label>
                    <Input
                      id="nif"
                      value={dadosIdentificativos.nif}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, nif: e.target.value }))}
                      placeholder="Número de identificação fiscal"
                    />
                  </div>
                </div>
              </div>

              {/* Secção 3: Informações Civis e Profissionais */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">Informações Civis e Profissionais</h3>

                {/* Sub-secção Estado Civil */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Estado Civil <span className="text-red-500">*</span></Label>
                    <RadioGroup
                      value={dadosIdentificativos.estadoCivil}
                      onValueChange={(value) => handleEstadoCivilChange(value)}
                      className="grid grid-cols-2 md:grid-cols-3 gap-2"
                    >
                      {['Solteiro(a)', 'Casado(a)', 'União de Factos', 'Viúvo(a)', 'Divorciado(a)', 'Separado(a) Judicialmente'].map((estado) => (
                        <div key={estado} className="flex items-center space-x-2">
                          <RadioGroupItem value={estado} id={`estado-${estado}`} />
                          <Label htmlFor={`estado-${estado}`} className="text-sm">{estado}</Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>

                  {/* Indicar Regime - Condicional */}
                  {dadosIdentificativos.mostrarRegimeCasamento && (
                    <div className="space-y-2 pl-4 border-l-2 border-gray-200">
                      <Label>Indicar Regime</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {['Comunhão Adquiridos', 'Comunhão Geral', 'Separação de Bens', 'Outros regimes'].map((regime) => (
                          <div key={regime} className="flex items-center space-x-2">
                            <Checkbox
                              id={`regime-${regime}`}
                              checked={dadosIdentificativos.regimeCasamento === regime}
                              onCheckedChange={(checked) => setDadosIdentificativos(prev => ({ ...prev, regimeCasamento: checked ? regime : '' }))}
                            />
                            <Label htmlFor={`regime-${regime}`} className="text-sm">{regime}</Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Sub-secção Situação Profissional */}
                <div className="space-y-4 mt-6">
                  <div className="space-y-2">
                    <Label>Exercer um Cargo Público? <span className="text-red-500">*</span></Label>
                    <RadioGroup
                      value={dadosIdentificativos.exerceCargoPublico}
                      onValueChange={(value) => handleCargoPublicoChange(value)}
                      className="flex items-center space-x-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Sim" id="cargo-sim" />
                        <Label htmlFor="cargo-sim">Sim</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="Não" id="cargo-nao" />
                        <Label htmlFor="cargo-nao">Não</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Campo Qual - Condicional */}
                  {dadosIdentificativos.mostrarCargoPublico && (
                    <div className="space-y-2 pl-4 border-l-2 border-gray-200">
                      <Label htmlFor="qual-cargo">Qual?</Label>
                      <Input
                        id="qual-cargo"
                        value={dadosIdentificativos.qualCargoPublico}
                        onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, qualCargoPublico: e.target.value }))}
                        placeholder="Especificar cargo público"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Secção 4: Endereço */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">Endereço</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="provincia">Província <span className="text-red-500">*</span></Label>
                    <Input
                      id="provincia"
                      value={dadosIdentificativos.provincia}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, provincia: e.target.value }))}
                      placeholder="Província"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="municipio">Município <span className="text-red-500">*</span></Label>
                    <Input
                      id="municipio"
                      value={dadosIdentificativos.municipio}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, municipio: e.target.value }))}
                      placeholder="Município"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bairro">Bairro</Label>
                    <Input
                      id="bairro"
                      value={dadosIdentificativos.bairro}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, bairro: e.target.value }))}
                      placeholder="Bairro"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rua">Rua</Label>
                    <Input
                      id="rua"
                      value={dadosIdentificativos.rua}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, rua: e.target.value }))}
                      placeholder="Rua"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="habilitacao" className="mt-6 space-y-8">

              {/* Secção 1: Habilitações Académicas */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Habilitações Académicas <span className="text-red-500">*</span></h3>
                <RadioGroup
                  value={habilitacao.habilitacaoAcademica}
                  onValueChange={handleHabilitacaoAcademicaChange}
                  className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="semEstudos" id="sem-estudos" />
                    <Label htmlFor="sem-estudos">S/Estudos</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="ensinoPrimario" id="ensino-primario" />
                    <Label htmlFor="ensino-primario">Ensino Primário</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="ensinoSecundario" id="ensino-secundario" />
                    <Label htmlFor="ensino-secundario">Ensino Secundário</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="ensinoMedio" id="ensino-medio" />
                    <Label htmlFor="ensino-medio">Ensino Médio</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="cursoSuperior" id="curso-superior" />
                    <Label htmlFor="curso-superior">Curso Superior</Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Secção 2: Situação Profissional */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">Situação Profissional <span className="text-red-500">*</span></h3>
                <RadioGroup
                  value={habilitacao.situacaoProfissional}
                  onValueChange={handleSituacaoProfissionalChange}
                  className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="estudante" id="estudante" />
                    <Label htmlFor="estudante">Estudante</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="reformado" id="reformado" />
                    <Label htmlFor="reformado">Reformado(a)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="domestico" id="domestico" />
                    <Label htmlFor="domestico">Doméstico(a)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="desempregado" id="desempregado" />
                    <Label htmlFor="desempregado">Desempregado(a)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="trabalhadorContaOutrem" id="trabalhador-conta-outrem" />
                    <Label htmlFor="trabalhador-conta-outrem">Trabalhador(a) Por Conta de Outrem</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="trabalhadorContaPropria" id="trabalhador-conta-propria" />
                    <Label htmlFor="trabalhador-conta-propria">Trabalhador(a) Por Conta Própria</Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Secção 3: Dados de Emprego - Condicional */}
              {habilitacao.mostrarDadosEmprego && (
                <div className="border-t pt-6">
                  <h3 className="text-lg font-semibold mb-4">Dados de Emprego</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="profissao">Profissão <span className="text-red-500">*</span></Label>
                        <Input
                          id="profissao"
                          value={habilitacao.profissao}
                          onChange={(e) => setHabilitacao(prev => ({ ...prev, profissao: e.target.value }))}
                          placeholder="Profissão"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="funcao">Função <span className="text-red-500">*</span></Label>
                        <Input
                          id="funcao"
                          value={habilitacao.funcao}
                          onChange={(e) => setHabilitacao(prev => ({ ...prev, funcao: e.target.value }))}
                          placeholder="Função"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="entidade-patronal">Entidade Patronal / Morada Sede <span className="text-red-500">*</span></Label>
                        <Input
                          id="entidade-patronal"
                          value={habilitacao.entidadePatronal}
                          onChange={(e) => setHabilitacao(prev => ({ ...prev, entidadePatronal: e.target.value }))}
                          placeholder="Entidade patronal"
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="cidade">Cidade <span className="text-red-500">*</span></Label>
                          <Input
                            id="cidade"
                            value={habilitacao.cidade}
                            onChange={(e) => setHabilitacao(prev => ({ ...prev, cidade: e.target.value }))}
                            placeholder="Cidade"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="pais">País <span className="text-red-500">*</span></Label>
                          <Input
                            id="pais"
                            value={habilitacao.pais}
                            onChange={(e) => setHabilitacao(prev => ({ ...prev, pais: e.target.value }))}
                            placeholder="País"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="rendimento-cliente">Rendimento do Cliente <span className="text-red-500">*</span></Label>
                        <Input
                          id="rendimento-cliente"
                          value={habilitacao.rendimentoCliente}
                          onChange={(e) => setHabilitacao(prev => ({ ...prev, rendimentoCliente: e.target.value }))}
                          placeholder="Rendimento mensal"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="natureza-rendimentos">Natureza do Rendimento <span className="text-red-500">*</span></Label>
                        <Select value={habilitacao.naturezaRendimento} onValueChange={(value) => setHabilitacao(prev => ({ ...prev, naturezaRendimento: value }))}>
                          <SelectTrigger>
                            <SelectValue placeholder="Seleciona Natureza" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="salario">Salário</SelectItem>
                            <SelectItem value="pensao">Pensão</SelectItem>
                            <SelectItem value="negocio">Negócio Próprio</SelectItem>
                            <SelectItem value="investimentos">Investimentos</SelectItem>
                            <SelectItem value="outros">Outros</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="ficheiros" className="mt-6 space-y-6">
              {tipoContaSelecionado === 'individual' ? (
                // Ficheiros para conta individual
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <Label htmlFor="imagem-assinatura">Imagem Assinatura <span className="text-red-500">*</span></Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <div className="space-y-2">
                        <input
                          id="imagem-assinatura"
                          type="file"
                          accept="image/*,.pdf"
                          onChange={(e) => handleFileUpload('imagemAssinatura', e)}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={() => document.getElementById('imagem-assinatura')?.click()}
                          type="button"
                        >
                          Escolher ficheiro
                        </Button>
                        <p className="text-sm text-gray-500">
                          {ficheiros.imagemAssinatura ? (
                            <span className="text-green-600 flex items-center justify-center gap-2">
                              ✓ {ficheiros.imagemAssinatura.name}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveFile('imagemAssinatura')}
                                className="text-red-500 hover:text-red-700 p-1 h-auto"
                              >
                                ✕
                              </Button>
                            </span>
                          ) : (
                            'Nenhum ficheiro selecionado'
                          )}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Campo condicional para Imagem BI */}
                  {dadosIdentificativos.tipoDocumento === 'BI' && (
                    <div className="space-y-4">
                      <Label htmlFor="imagem-bi">Imagem BI <span className="text-red-500">*</span></Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <div className="space-y-2">
                          <input
                            id="imagem-bi"
                            type="file"
                            accept="image/*,.pdf"
                            onChange={(e) => handleFileUpload('imagemBI', e)}
                            className="hidden"
                          />
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={() => document.getElementById('imagem-bi')?.click()}
                            type="button"
                          >
                            Escolher ficheiro
                          </Button>
                          <p className="text-sm text-gray-500">
                            {ficheiros.imagemBI ? (
                              <span className="text-green-600 flex items-center justify-center gap-2">
                                ✓ {ficheiros.imagemBI.name}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemoveFile('imagemBI')}
                                  className="text-red-500 hover:text-red-700 p-1 h-auto"
                                >
                                  ✕
                                </Button>
                              </span>
                            ) : (
                              'Nenhum ficheiro selecionado'
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Campo condicional para Imagem Passaporte */}
                  {dadosIdentificativos.tipoDocumento === 'Passaporte' && (
                    <div className="space-y-4">
                      <Label htmlFor="imagem-passaporte">Imagem Passaporte <span className="text-red-500">*</span></Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <div className="space-y-2">
                          <input
                            id="imagem-passaporte"
                            type="file"
                            accept="image/*,.pdf"
                            onChange={(e) => handleFileUpload('imagemPassaporte', e)}
                            className="hidden"
                          />
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={() => document.getElementById('imagem-passaporte')?.click()}
                            type="button"
                          >
                            Escolher ficheiro
                          </Button>
                          <p className="text-sm text-gray-500">
                            {ficheiros.imagemPassaporte ? (
                              <span className="text-green-600 flex items-center justify-center gap-2">
                                ✓ {ficheiros.imagemPassaporte.name}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemoveFile('imagemPassaporte')}
                                  className="text-red-500 hover:text-red-700 p-1 h-auto"
                                >
                                  ✕
                                </Button>
                              </span>
                            ) : (
                              'Nenhum ficheiro selecionado'
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Campo condicional para Imagem Cartão de Residente */}
                  {dadosIdentificativos.tipoDocumento === 'C.Residente' && (
                    <div className="space-y-4">
                      <Label htmlFor="imagem-cartao-residente">Imagem Cartão de Residente <span className="text-red-500">*</span></Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <div className="space-y-2">
                          <input
                            id="imagem-cartao-residente"
                            type="file"
                            accept="image/*,.pdf"
                            onChange={(e) => handleFileUpload('imagemCartaoResidente', e)}
                            className="hidden"
                          />
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={() => document.getElementById('imagem-cartao-residente')?.click()}
                            type="button"
                          >
                            Escolher ficheiro
                          </Button>
                          <p className="text-sm text-gray-500">
                            {ficheiros.imagemCartaoResidente ? (
                              <span className="text-green-600 flex items-center justify-center gap-2">
                                ✓ {ficheiros.imagemCartaoResidente.name}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemoveFile('imagemCartaoResidente')}
                                  className="text-red-500 hover:text-red-700 p-1 h-auto"
                                >
                                  ✕
                                </Button>
                              </span>
                            ) : (
                              'Nenhum ficheiro selecionado'
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                  <div className="space-y-4">
                    <Label htmlFor="imagem-declaracao">Imagem Declaração Serviço <span className="text-red-500">*</span></Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <div className="space-y-2">
                        <input
                          id="imagem-declaracao"
                          type="file"
                          accept="image/*,.pdf"
                          onChange={(e) => handleFileUpload('imagemDeclaracaoServico', e)}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={() => document.getElementById('imagem-declaracao')?.click()}
                          type="button"
                        >
                          Escolher ficheiro
                        </Button>
                        <p className="text-sm text-gray-500">
                          {ficheiros.imagemDeclaracaoServico ? (
                            <span className="text-green-600 flex items-center justify-center gap-2">
                              ✓ {ficheiros.imagemDeclaracaoServico.name}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveFile('imagemDeclaracaoServico')}
                                className="text-red-500 hover:text-red-700 p-1 h-auto"
                              >
                                ✕
                              </Button>
                            </span>
                          ) : (
                            'Nenhum ficheiro selecionado'
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                // Ficheiros para conta conjunta - duplicados por titular
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Documentos do 1º Titular</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* Assinatura do 1º Titular - sempre obrigatório */}
                      <div className="space-y-4">
                        <Label htmlFor="imagem-assinatura-1">Assinatura do 1º Titular <span className="text-red-500">*</span></Label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <div className="space-y-2">
                            <input
                              id="imagem-assinatura-1"
                              type="file"
                              accept="image/*,.pdf"
                              onChange={(e) => handleFileUpload('imagemAssinatura', e)}
                              className="hidden"
                            />
                            <Button
                              variant="outline"
                              className="w-full"
                              onClick={() => document.getElementById('imagem-assinatura-1')?.click()}
                              type="button"
                            >
                              Escolher ficheiro
                            </Button>
                            <p className="text-sm text-gray-500">
                              {ficheiros.imagemAssinatura ? (
                                <span className="text-green-600 flex items-center justify-center gap-2">
                                  ✓ {ficheiros.imagemAssinatura.name}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleRemoveFile('imagemAssinatura')}
                                    className="text-red-500 hover:text-red-700 p-1 h-auto"
                                  >
                                    ✕
                                  </Button>
                                </span>
                              ) : (
                                'Nenhum ficheiro selecionado'
                              )}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Campo condicional para documento de identificação do 1º Titular */}
                      {dadosIdentificativos.tipoDocumento === 'BI' && (
                        <div className="space-y-4">
                          <Label htmlFor="imagem-bi-1">BI do 1º Titular <span className="text-red-500">*</span></Label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <div className="space-y-2">
                              <input
                                id="imagem-bi-1"
                                type="file"
                                accept="image/*,.pdf"
                                onChange={(e) => handleFileUpload('imagemBI', e)}
                                className="hidden"
                              />
                              <Button
                                variant="outline"
                                className="w-full"
                                onClick={() => document.getElementById('imagem-bi-1')?.click()}
                                type="button"
                              >
                                Escolher ficheiro
                              </Button>
                              <p className="text-sm text-gray-500">
                                {ficheiros.imagemBI ? (
                                  <span className="text-green-600 flex items-center justify-center gap-2">
                                    ✓ {ficheiros.imagemBI.name}
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveFile('imagemBI')}
                                      className="text-red-500 hover:text-red-700 p-1 h-auto"
                                    >
                                      ✕
                                    </Button>
                                  </span>
                                ) : (
                                  'Nenhum ficheiro selecionado'
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {dadosIdentificativos.tipoDocumento === 'Passaporte' && (
                        <div className="space-y-4">
                          <Label htmlFor="imagem-passaporte-1">Passaporte do 1º Titular <span className="text-red-500">*</span></Label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <div className="space-y-2">
                              <input
                                id="imagem-passaporte-1"
                                type="file"
                                accept="image/*,.pdf"
                                onChange={(e) => handleFileUpload('imagemPassaporte', e)}
                                className="hidden"
                              />
                              <Button
                                variant="outline"
                                className="w-full"
                                onClick={() => document.getElementById('imagem-passaporte-1')?.click()}
                                type="button"
                              >
                                Escolher ficheiro
                              </Button>
                              <p className="text-sm text-gray-500">
                                {ficheiros.imagemPassaporte ? (
                                  <span className="text-green-600 flex items-center justify-center gap-2">
                                    ✓ {ficheiros.imagemPassaporte.name}
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveFile('imagemPassaporte')}
                                      className="text-red-500 hover:text-red-700 p-1 h-auto"
                                    >
                                      ✕
                                    </Button>
                                  </span>
                                ) : (
                                  'Nenhum ficheiro selecionado'
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {dadosIdentificativos.tipoDocumento === 'C.Residente' && (
                        <div className="space-y-4">
                          <Label htmlFor="imagem-cartao-residente-1">Cartão de Residente do 1º Titular <span className="text-red-500">*</span></Label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <div className="space-y-2">
                              <input
                                id="imagem-cartao-residente-1"
                                type="file"
                                accept="image/*,.pdf"
                                onChange={(e) => handleFileUpload('imagemCartaoResidente', e)}
                                className="hidden"
                              />
                              <Button
                                variant="outline"
                                className="w-full"
                                onClick={() => document.getElementById('imagem-cartao-residente-1')?.click()}
                                type="button"
                              >
                                Escolher ficheiro
                              </Button>
                              <p className="text-sm text-gray-500">
                                {ficheiros.imagemCartaoResidente ? (
                                  <span className="text-green-600 flex items-center justify-center gap-2">
                                    ✓ {ficheiros.imagemCartaoResidente.name}
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveFile('imagemCartaoResidente')}
                                      className="text-red-500 hover:text-red-700 p-1 h-auto"
                                    >
                                      ✕
                                    </Button>
                                  </span>
                                ) : (
                                  'Nenhum ficheiro selecionado'
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Declaração de Serviço do 1º Titular - sempre obrigatório */}
                      <div className="space-y-4">
                        <Label htmlFor="imagem-declaracao-servico-1">Declaração Serviço do 1º Titular <span className="text-red-500">*</span></Label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <div className="space-y-2">
                            <input
                              id="imagem-declaracao-servico-1"
                              type="file"
                              accept="image/*,.pdf"
                              onChange={(e) => handleFileUpload('imagemDeclaracaoServico', e)}
                              className="hidden"
                            />
                            <Button
                              variant="outline"
                              className="w-full"
                              onClick={() => document.getElementById('imagem-declaracao-servico-1')?.click()}
                              type="button"
                            >
                              Escolher ficheiro
                            </Button>
                            <p className="text-sm text-gray-500">
                              {ficheiros.imagemDeclaracaoServico ? (
                                <span className="text-green-600 flex items-center justify-center gap-2">
                                  ✓ {ficheiros.imagemDeclaracaoServico.name}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleRemoveFile('imagemDeclaracaoServico')}
                                    className="text-red-500 hover:text-red-700 p-1 h-auto"
                                  >
                                    ✕
                                  </Button>
                                </span>
                              ) : (
                                'Nenhum ficheiro selecionado'
                              )}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Documentos do 2º Titular</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* Assinatura do 2º Titular - sempre obrigatório */}
                      <div className="space-y-4">
                        <Label htmlFor="imagem-assinatura-2">Assinatura do 2º Titular <span className="text-red-500">*</span></Label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <div className="space-y-2">
                            <input
                              id="imagem-assinatura-2"
                              type="file"
                              accept="image/*,.pdf"
                              onChange={(e) => handleFileUploadSegundoTitular('imagemAssinatura', e)}
                              className="hidden"
                            />
                            <Button
                              variant="outline"
                              className="w-full"
                              onClick={() => document.getElementById('imagem-assinatura-2')?.click()}
                              type="button"
                            >
                              Escolher ficheiro
                            </Button>
                            <p className="text-sm text-gray-500">
                              {ficheirosSegundoTitular.imagemAssinatura ? (
                                <span className="text-green-600 flex items-center justify-center gap-2">
                                  ✓ {ficheirosSegundoTitular.imagemAssinatura.name}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleRemoveFileSegundoTitular('imagemAssinatura')}
                                    className="text-red-500 hover:text-red-700 p-1 h-auto"
                                  >
                                    ✕
                                  </Button>
                                </span>
                              ) : (
                                'Nenhum ficheiro selecionado'
                              )}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Campo condicional para documento de identificação do 2º Titular */}
                      {dadosSegundoTitular.tipoDocumento === 'BI' && (
                        <div className="space-y-4">
                          <Label htmlFor="imagem-bi-2">BI do 2º Titular <span className="text-red-500">*</span></Label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <div className="space-y-2">
                              <input
                                id="imagem-bi-2"
                                type="file"
                                accept="image/*,.pdf"
                                onChange={(e) => handleFileUploadSegundoTitular('imagemBI', e)}
                                className="hidden"
                              />
                              <Button
                                variant="outline"
                                className="w-full"
                                onClick={() => document.getElementById('imagem-bi-2')?.click()}
                                type="button"
                              >
                                Escolher ficheiro
                              </Button>
                              <p className="text-sm text-gray-500">
                                {ficheirosSegundoTitular.imagemBI ? (
                                  <span className="text-green-600 flex items-center justify-center gap-2">
                                    ✓ {ficheirosSegundoTitular.imagemBI.name}
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveFileSegundoTitular('imagemBI')}
                                      className="text-red-500 hover:text-red-700 p-1 h-auto"
                                    >
                                      ✕
                                    </Button>
                                  </span>
                                ) : (
                                  'Nenhum ficheiro selecionado'
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {dadosSegundoTitular.tipoDocumento === 'Passaporte' && (
                        <div className="space-y-4">
                          <Label htmlFor="imagem-passaporte-2">Passaporte do 2º Titular <span className="text-red-500">*</span></Label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <div className="space-y-2">
                              <input
                                id="imagem-passaporte-2"
                                type="file"
                                accept="image/*,.pdf"
                                onChange={(e) => handleFileUploadSegundoTitular('imagemPassaporte', e)}
                                className="hidden"
                              />
                              <Button
                                variant="outline"
                                className="w-full"
                                onClick={() => document.getElementById('imagem-passaporte-2')?.click()}
                                type="button"
                              >
                                Escolher ficheiro
                              </Button>
                              <p className="text-sm text-gray-500">
                                {ficheirosSegundoTitular.imagemPassaporte ? (
                                  <span className="text-green-600 flex items-center justify-center gap-2">
                                    ✓ {ficheirosSegundoTitular.imagemPassaporte.name}
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveFileSegundoTitular('imagemPassaporte')}
                                      className="text-red-500 hover:text-red-700 p-1 h-auto"
                                    >
                                      ✕
                                    </Button>
                                  </span>
                                ) : (
                                  'Nenhum ficheiro selecionado'
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {dadosSegundoTitular.tipoDocumento === 'C.Residente' && (
                        <div className="space-y-4">
                          <Label htmlFor="imagem-cartao-residente-2">Cartão de Residente do 2º Titular <span className="text-red-500">*</span></Label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <div className="space-y-2">
                              <input
                                id="imagem-cartao-residente-2"
                                type="file"
                                accept="image/*,.pdf"
                                onChange={(e) => handleFileUploadSegundoTitular('imagemCartaoResidente', e)}
                                className="hidden"
                              />
                              <Button
                                variant="outline"
                                className="w-full"
                                onClick={() => document.getElementById('imagem-cartao-residente-2')?.click()}
                                type="button"
                              >
                                Escolher ficheiro
                              </Button>
                              <p className="text-sm text-gray-500">
                                {ficheirosSegundoTitular.imagemCartaoResidente ? (
                                  <span className="text-green-600 flex items-center justify-center gap-2">
                                    ✓ {ficheirosSegundoTitular.imagemCartaoResidente.name}
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveFileSegundoTitular('imagemCartaoResidente')}
                                      className="text-red-500 hover:text-red-700 p-1 h-auto"
                                    >
                                      ✕
                                    </Button>
                                  </span>
                                ) : (
                                  'Nenhum ficheiro selecionado'
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Declaração de Serviço do 2º Titular - sempre obrigatório */}
                      <div className="space-y-4">
                        <Label htmlFor="imagem-declaracao-servico-2">Declaração Serviço do 2º Titular <span className="text-red-500">*</span></Label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          <div className="space-y-2">
                            <input
                              id="imagem-declaracao-servico-2"
                              type="file"
                              accept="image/*,.pdf"
                              onChange={(e) => handleFileUploadSegundoTitular('imagemDeclaracaoServico', e)}
                              className="hidden"
                            />
                            <Button
                              variant="outline"
                              className="w-full"
                              onClick={() => document.getElementById('imagem-declaracao-servico-2')?.click()}
                              type="button"
                            >
                              Escolher ficheiro
                            </Button>
                            <p className="text-sm text-gray-500">
                              {ficheirosSegundoTitular.imagemDeclaracaoServico ? (
                                <span className="text-green-600 flex items-center justify-center gap-2">
                                  ✓ {ficheirosSegundoTitular.imagemDeclaracaoServico.name}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleRemoveFileSegundoTitular('imagemDeclaracaoServico')}
                                    className="text-red-500 hover:text-red-700 p-1 h-auto"
                                  >
                                    ✕
                                  </Button>
                                </span>
                              ) : (
                                'Nenhum ficheiro selecionado'
                              )}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="validacao" className="mt-6">
              <div className="text-center py-8 text-gray-500">
                <p>Aba Validação de Dados - Implementar validação final</p>
              </div>
            </TabsContent>
            {/* Aba 2º Titular - para conta junior ou conjunta */}
            {(dadosConta.tipoConta === 'junior' || tipoContaSelecionado === 'conjunta') && (
              <TabsContent value="segundo-titular" className="mt-6 space-y-8">
                <div>
                  <h2 className="text-xl font-semibold mb-6">Dados do 2º Titular</h2>

                  {/* Secção 1: Identificação Pessoal do 2º Titular */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Identificação Pessoal</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="nome-2">Nome <span className="text-red-500">*</span></Label>
                        <Input
                          id="nome-2"
                          value={dadosSegundoTitular.nome}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, nome: e.target.value }))}
                          placeholder="Nome completo do 2º titular"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="data-nascimento-2">Data de Nascimento <span className="text-red-500">*</span></Label>
                        <Input
                          id="data-nascimento-2"
                          type="date"
                          value={dadosSegundoTitular.dataNascimento}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, dataNascimento: e.target.value }))}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Sexo <span className="text-red-500">*</span></Label>
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="sexo-m-2"
                              checked={dadosSegundoTitular.sexo === 'M'}
                              onCheckedChange={(checked) => setDadosSegundoTitular(prev => ({ ...prev, sexo: checked ? 'M' : '' }))}
                            />
                            <Label htmlFor="sexo-m-2">M</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="sexo-f-2"
                              checked={dadosSegundoTitular.sexo === 'F'}
                              onCheckedChange={(checked) => setDadosSegundoTitular(prev => ({ ...prev, sexo: checked ? 'F' : '' }))}
                            />
                            <Label htmlFor="sexo-f-2">F</Label>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="nacionalidade-2">Nacionalidade <span className="text-red-500">*</span></Label>
                        <Input
                          id="nacionalidade-2"
                          value={dadosSegundoTitular.nacionalidade}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, nacionalidade: e.target.value }))}
                          placeholder="Nacionalidade"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="naturalidade-2">Naturalidade</Label>
                        <Input
                          id="naturalidade-2"
                          value={dadosSegundoTitular.naturalidade}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, naturalidade: e.target.value }))}
                          placeholder="Naturalidade"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Secção 2: Documento de Identificação do 2º Titular */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Documento de Identificação</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>Tipo de Documento <span className="text-red-500">*</span></Label>
                        <RadioGroup
                          value={dadosSegundoTitular.tipoDocumento}
                          onValueChange={(value) => setDadosSegundoTitular(prev => ({ ...prev, tipoDocumento: value }))}
                          className="flex items-center space-x-4"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="BI" id="doc-bi-2" />
                            <Label htmlFor="doc-bi-2">BI</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="Passaporte" id="doc-passaporte-2" />
                            <Label htmlFor="doc-passaporte-2">Passaporte</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="C.Residente" id="doc-residente-2" />
                            <Label htmlFor="doc-residente-2">C.Residente</Label>
                          </div>
                        </RadioGroup>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="numero-identificacao-2">Nº de Identificação <span className="text-red-500">*</span></Label>
                        <Input
                          id="numero-identificacao-2"
                          value={dadosSegundoTitular.numeroIdentificacao}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, numeroIdentificacao: e.target.value }))}
                          placeholder="Número de identificação"
                          disabled={!dadosSegundoTitular.tipoDocumento}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="local-emissao-2">Local de Emissão <span className="text-red-500">*</span></Label>
                        <Input
                          id="local-emissao-2"
                          value={dadosSegundoTitular.localEmissao}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, localEmissao: e.target.value }))}
                          placeholder="Local de emissão"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="data-emissao-2">Data de Emissão <span className="text-red-500">*</span></Label>
                        <Input
                          id="data-emissao-2"
                          type="date"
                          value={dadosSegundoTitular.dataEmissao}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, dataEmissao: e.target.value }))}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="data-validade-2">Data de Validade <span className="text-red-500">*</span></Label>
                        <Input
                          id="data-validade-2"
                          type="date"
                          value={dadosSegundoTitular.dataValidade}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, dataValidade: e.target.value }))}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="nif-2">NIF <span className="text-red-500">*</span></Label>
                        <Input
                          id="nif-2"
                          value={dadosSegundoTitular.nif}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, nif: e.target.value }))}
                          placeholder="Número de identificação fiscal"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Secção 3: Informações Civis e Profissionais do 2º Titular */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Informações Civis e Profissionais</h3>

                    {/* Sub-secção Estado Civil */}
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>Estado Civil <span className="text-red-500">*</span></Label>
                        <RadioGroup
                          value={dadosSegundoTitular.estadoCivil}
                          onValueChange={(value) => {
                            setDadosSegundoTitular(prev => ({
                              ...prev,
                              estadoCivil: value,
                              mostrarRegimeCasamento: value === 'Casado(a)' || value === 'União de Factos'
                            }));
                          }}
                          className="grid grid-cols-2 md:grid-cols-3 gap-2"
                        >
                          {['Solteiro(a)', 'Casado(a)', 'União de Factos', 'Viúvo(a)', 'Divorciado(a)', 'Separado(a) Judicialmente'].map((estado) => (
                            <div key={estado} className="flex items-center space-x-2">
                              <RadioGroupItem value={estado} id={`estado-2-${estado}`} />
                              <Label htmlFor={`estado-2-${estado}`} className="text-sm">{estado}</Label>
                            </div>
                          ))}
                        </RadioGroup>
                      </div>

                      {/* Indicar Regime - Condicional */}
                      {dadosSegundoTitular.mostrarRegimeCasamento && (
                        <div className="space-y-2 pl-4 border-l-2 border-gray-200">
                          <Label>Indicar Regime</Label>
                          <div className="grid grid-cols-2 gap-2">
                            {['Comunhão Adquiridos', 'Comunhão Geral', 'Separação de Bens', 'Outros regimes'].map((regime) => (
                              <div key={regime} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`regime-2-${regime}`}
                                  checked={dadosSegundoTitular.regimeCasamento === regime}
                                  onCheckedChange={(checked) => setDadosSegundoTitular(prev => ({ ...prev, regimeCasamento: checked ? regime : '' }))}
                                />
                                <Label htmlFor={`regime-2-${regime}`} className="text-sm">{regime}</Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Sub-secção Situação Profissional */}
                    <div className="space-y-4 mt-6">
                      <div className="space-y-2">
                        <Label>Exercer um Cargo Público? <span className="text-red-500">*</span></Label>
                        <RadioGroup
                          value={dadosSegundoTitular.exerceCargoPublico}
                          onValueChange={(value) => {
                            setDadosSegundoTitular(prev => ({
                              ...prev,
                              exerceCargoPublico: value,
                              mostrarCargoPublico: value === 'Sim'
                            }));
                          }}
                          className="flex items-center space-x-4"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="Sim" id="cargo-sim-2" />
                            <Label htmlFor="cargo-sim-2">Sim</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="Não" id="cargo-nao-2" />
                            <Label htmlFor="cargo-nao-2">Não</Label>
                          </div>
                        </RadioGroup>
                      </div>

                      {/* Qual Cargo - Condicional */}
                      {dadosSegundoTitular.mostrarCargoPublico && (
                        <div className="space-y-2 pl-4 border-l-2 border-gray-200">
                          <Label htmlFor="qual-cargo-2">Qual Cargo?</Label>
                          <Input
                            id="qual-cargo-2"
                            value={dadosSegundoTitular.qualCargoPublico}
                            onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, qualCargoPublico: e.target.value }))}
                            placeholder="Especifique o cargo público"
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Secção 4: Contactos do 2º Titular */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Contactos</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      {/* Telefones Pessoais */}
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="telefone-personal-2">Telefone Pessoal <span className="text-red-500">*</span></Label>
                          <Input
                            id="telefone-personal-2"
                            value={contactosSegundoTitular.telefonePersonal}
                            onChange={(e) => setContactosSegundoTitular(prev => ({ ...prev, telefonePersonal: e.target.value }))}
                            placeholder="Telefone pessoal"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email-personal-2">Email Pessoal <span className="text-red-500">*</span></Label>
                          <Input
                            id="email-personal-2"
                            type="email"
                            value={contactosSegundoTitular.emailPersonal}
                            onChange={(e) => setContactosSegundoTitular(prev => ({ ...prev, emailPersonal: e.target.value }))}
                            placeholder="Email pessoal"
                          />
                        </div>
                      </div>

                      {/* Telefones Profissionais */}
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="telefone-profissional-2">Telefone Profissional</Label>
                          <Input
                            id="telefone-profissional-2"
                            value={contactosSegundoTitular.telefoneProfissional}
                            onChange={(e) => setContactosSegundoTitular(prev => ({ ...prev, telefoneProfissional: e.target.value }))}
                            placeholder="Telefone profissional"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email-profissional-2">Email Profissional</Label>
                          <Input
                            id="email-profissional-2"
                            type="email"
                            value={contactosSegundoTitular.emailProfissional}
                            onChange={(e) => setContactosSegundoTitular(prev => ({ ...prev, emailProfissional: e.target.value }))}
                            placeholder="Email profissional"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Secção 5: Habilitação e Dados Profissionais do 2º Titular */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Habilitação e Dados Profissionais</h3>

                    {/* Secção 1: Habilitações Académicas */}
                    <div>
                      <h4 className="text-md font-semibold mb-4">Habilitações Académicas <span className="text-red-500">*</span></h4>
                      <RadioGroup
                        value={habilitacaoSegundoTitular.habilitacaoAcademica}
                        onValueChange={handleHabilitacaoAcademicaChangeSegundoTitular}
                        className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="semEstudos" id="sem-estudos-2" />
                          <Label htmlFor="sem-estudos-2">S/Estudos</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="ensinoPrimario" id="ensino-primario-2" />
                          <Label htmlFor="ensino-primario-2">Ensino Primário</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="ensinoSecundario" id="ensino-secundario-2" />
                          <Label htmlFor="ensino-secundario-2">Ensino Secundário</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="ensinoMedio" id="ensino-medio-2" />
                          <Label htmlFor="ensino-medio-2">Ensino Médio</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="cursoSuperior" id="curso-superior-2" />
                          <Label htmlFor="curso-superior-2">Curso Superior</Label>
                        </div>
                      </RadioGroup>
                    </div>

                    {/* Secção 2: Situação Profissional */}
                    <div className="border-t pt-6 mt-6">
                      <h4 className="text-md font-semibold mb-4">Situação Profissional <span className="text-red-500">*</span></h4>
                      <RadioGroup
                        value={habilitacaoSegundoTitular.situacaoProfissional}
                        onValueChange={handleSituacaoProfissionalChangeSegundoTitular}
                        className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="estudante" id="estudante-2" />
                          <Label htmlFor="estudante-2">Estudante</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="reformado" id="reformado-2" />
                          <Label htmlFor="reformado-2">Reformado(a)</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="domestico" id="domestico-2" />
                          <Label htmlFor="domestico-2">Doméstico(a)</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="desempregado" id="desempregado-2" />
                          <Label htmlFor="desempregado-2">Desempregado(a)</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="trabalhadorContaOutrem" id="trabalhador-conta-outrem-2" />
                          <Label htmlFor="trabalhador-conta-outrem-2">Trabalhador(a) Por Conta de Outrem</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="trabalhadorContaPropria" id="trabalhador-conta-propria-2" />
                          <Label htmlFor="trabalhador-conta-propria-2">Trabalhador(a) Por Conta Própria</Label>
                        </div>
                      </RadioGroup>
                    </div>

                    {/* Secção 3: Dados de Emprego - Condicional */}
                    {habilitacaoSegundoTitular.mostrarDadosEmprego && (
                      <div className="border-t pt-6 mt-6">
                        <h4 className="text-md font-semibold mb-4">Dados de Emprego</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <div className="space-y-2">
                              <Label htmlFor="profissao-2">Profissão <span className="text-red-500">*</span></Label>
                              <Input
                                id="profissao-2"
                                value={habilitacaoSegundoTitular.profissao}
                                onChange={(e) => setHabilitacaoSegundoTitular(prev => ({ ...prev, profissao: e.target.value }))}
                                placeholder="Profissão"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="funcao-2">Função <span className="text-red-500">*</span></Label>
                              <Input
                                id="funcao-2"
                                value={habilitacaoSegundoTitular.funcao}
                                onChange={(e) => setHabilitacaoSegundoTitular(prev => ({ ...prev, funcao: e.target.value }))}
                                placeholder="Função"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="entidade-patronal-2">Entidade Patronal / Morada Sede <span className="text-red-500">*</span></Label>
                              <Input
                                id="entidade-patronal-2"
                                value={habilitacaoSegundoTitular.entidadePatronal}
                                onChange={(e) => setHabilitacaoSegundoTitular(prev => ({ ...prev, entidadePatronal: e.target.value }))}
                                placeholder="Entidade patronal"
                              />
                            </div>
                          </div>

                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="cidade-2">Cidade <span className="text-red-500">*</span></Label>
                                <Input
                                  id="cidade-2"
                                  value={habilitacaoSegundoTitular.cidade}
                                  onChange={(e) => setHabilitacaoSegundoTitular(prev => ({ ...prev, cidade: e.target.value }))}
                                  placeholder="Cidade"
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="pais-2">País <span className="text-red-500">*</span></Label>
                                <Input
                                  id="pais-2"
                                  value={habilitacaoSegundoTitular.pais}
                                  onChange={(e) => setHabilitacaoSegundoTitular(prev => ({ ...prev, pais: e.target.value }))}
                                  placeholder="País"
                                />
                              </div>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="rendimento-cliente-2">Rendimento do Cliente <span className="text-red-500">*</span></Label>
                              <Input
                                id="rendimento-cliente-2"
                                value={habilitacaoSegundoTitular.rendimentoCliente}
                                onChange={(e) => setHabilitacaoSegundoTitular(prev => ({ ...prev, rendimentoCliente: e.target.value }))}
                                placeholder="Rendimento mensal"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="natureza-rendimentos-2">Natureza do Rendimento <span className="text-red-500">*</span></Label>
                              <Select value={habilitacaoSegundoTitular.naturezaRendimento} onValueChange={(value) => setHabilitacaoSegundoTitular(prev => ({ ...prev, naturezaRendimento: value }))}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Seleciona Natureza" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="salario">Salário</SelectItem>
                                  <SelectItem value="pensao">Pensão</SelectItem>
                                  <SelectItem value="negocio">Negócio Próprio</SelectItem>
                                  <SelectItem value="investimentos">Investimentos</SelectItem>
                                  <SelectItem value="outros">Outros</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Secção 6: Endereço do 2º Titular */}
                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Endereço</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="provincia-2">Província <span className="text-red-500">*</span></Label>
                        <Input
                          id="provincia-2"
                          value={dadosSegundoTitular.provincia}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, provincia: e.target.value }))}
                          placeholder="Província"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="municipio-2">Município <span className="text-red-500">*</span></Label>
                        <Input
                          id="municipio-2"
                          value={dadosSegundoTitular.municipio}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, municipio: e.target.value }))}
                          placeholder="Município"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="bairro-2">Bairro</Label>
                        <Input
                          id="bairro-2"
                          value={dadosSegundoTitular.bairro}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, bairro: e.target.value }))}
                          placeholder="Bairro"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="rua-2">Rua</Label>
                        <Input
                          id="rua-2"
                          value={dadosSegundoTitular.rua}
                          onChange={(e) => setDadosSegundoTitular(prev => ({ ...prev, rua: e.target.value }))}
                          placeholder="Rua"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            )}
          </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Botão Terminar Registo - fora das abas */}
      {tipoContaSelecionado && (
        <div className="flex flex-col items-center pt-6 border-t space-y-4">
          {/* Indicador de progresso do botão */}
          <div className="text-center">
            <div className="text-sm text-gray-600 mb-1">
              {calculateProgress() === 100 ? (
                <span className="text-green-600 font-medium">✓ Formulário completo - Pronto para submeter</span>
              ) : (
                <span>Preencha todos os campos obrigatórios ({calculateProgress()}% completo)</span>
              )}
            </div>
          </div>

          <Button
            onClick={handleTerminarRegistro}
            disabled={isSubmitting || loadingBranches}
            className={`px-8 py-3 text-lg disabled:opacity-50 transition-all duration-300 ${
              calculateProgress() === 100
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-5 w-5 animate-spin mr-2" />
                Criando Conta...
              </>
            ) : (
              <>
                {calculateProgress() === 100 && <CheckCircle className="h-5 w-5 mr-2" />}
                Terminar Registo
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

export default AbrirContaParticular;
