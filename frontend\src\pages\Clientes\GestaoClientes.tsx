
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Users, Search, Plus, Filter, Download, Eye, Edit,
  Building2, User, Phone, Mail, MapPin, Calendar,
  ChevronLeft, ChevronRight, RefreshCw
} from 'lucide-react';
import { Client, ClientFilters, ClientStats } from '@/types/client';
import { clientService } from '@/services/clientService';
import { useConfirm } from '@/contexts/ConfirmDialogContext';
import ClientViewModal from '@/components/modals/ClientViewModal';
import ClientEditModal from '@/components/modals/ClientEditModal';
import NewClientModal from '@/components/modals/NewClientModal';
import ClientDataTable from '@/components/clients/ClientDataTable';
import { formatDate } from '@/utils/dateUtils';

const GestaoClientes = () => {
  const { toast } = useToast();
  const { confirm } = useConfirm();
  const [clients, setClients] = useState<Client[]>([]);
  const [stats, setStats] = useState<ClientStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showNewClientModal, setShowNewClientModal] = useState(false);
  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_records: 0,
    records_per_page: 20,
    has_next: false,
    has_previous: false
  });

  const [filters, setFilters] = useState<ClientFilters>({
    search: '',
    client_type: undefined,
    status: 'active', // Filtrar apenas clientes ativos por padrão
    branch_id: undefined,
    start_date: '',
    end_date: '',
    page: 1,
    limit: 20
  });

  // Carregar clientes
  const loadClients = async () => {
    setLoading(true);
    try {
      const response = await clientService.getClients(filters);
      setClients(response.clients);
      setPagination(response.pagination);
    } catch (error) {
      toast({
        title: "Erro ao carregar clientes",
        description: error instanceof Error ? error.message : "Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar estatísticas
  const loadStats = async () => {
    try {
      const statsData = await clientService.getClientStats();
      setStats(statsData);
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    }
  };

  // Efeito para carregar dados iniciais
  useEffect(() => {
    loadClients();
    loadStats();
  }, []);

  // Efeito para recarregar quando filtros mudarem
  useEffect(() => {
    loadClients();
  }, [filters]);

  // Função para aplicar filtros
  const handleSearch = () => {
    setFilters(prev => ({ ...prev, page: 1 }));
  };

  // Função para limpar filtros
  const clearFilters = () => {
    setFilters({
      search: '',
      client_type: undefined,
      status: 'active', // Manter filtro de clientes ativos por padrão
      branch_id: undefined,
      start_date: '',
      end_date: '',
      page: 1,
      limit: 20
    });
  };

  // Função para mudar página
  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  // Função para exportar
  const handleExport = async () => {
    try {
      await clientService.exportClients(filters);
      toast({
        title: "Exportação concluída",
        description: "Lista de clientes exportada com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro na exportação",
        description: error instanceof Error ? error.message : "Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // Usar utilitário centralizado para formatação de data
  // A função formatDate já está importada do dateUtils

  // Função para obter badge de status
  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'default',
      inactive: 'secondary',
      blocked: 'destructive',
      pending: 'outline',
      rejected: 'destructive'
    } as const;

    const labels = {
      active: 'Ativo',
      inactive: 'Inativo',
      blocked: 'Bloqueado',
      pending: 'Pendente',
      rejected: 'Rejeitado'
    };

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {labels[status as keyof typeof labels]}
      </Badge>
    );
  };

  // Handlers para ações dos clientes
  const handleViewClient = (client: Client) => {
    setSelectedClient(client);
    setShowViewModal(true);
  };

  const handleEditClient = (client: Client) => {
    setSelectedClient(client);
    setShowEditModal(true);
  };

  const handleDeleteClient = async (client: Client) => {
    const confirmed = await confirm({
      title: 'Confirmar Exclusão',
      message: `Tem certeza que deseja excluir o cliente "${client.full_name || client.company_name}"?`,
      confirmText: 'Sim, Excluir',
      cancelText: 'Cancelar',
      type: 'destructive'
    });

    if (confirmed) {
      try {
        await clientService.deleteClient(client.id);

        toast({
          title: "Cliente excluído",
          description: "O cliente foi removido com sucesso.",
        });

        // Recarregar a lista de clientes
        loadClients();
      } catch (error) {
        toast({
          title: "Erro ao excluir cliente",
          description: error instanceof Error ? error.message : "Tente novamente.",
          variant: "destructive"
        });
      }
    }
  };

  const handleNewClient = () => {
    setShowNewClientModal(true);
  };

  const handleCloseModals = () => {
    setSelectedClient(null);
    setShowViewModal(false);
    setShowEditModal(false);
    setShowNewClientModal(false);
  };

  // Handlers para DataTable
  const handleSort = (column: string, direction: 'asc' | 'desc') => {
    // Implementar ordenação no futuro
    console.log('Sort:', column, direction);
  };

  const handleFilter = (filters: Record<string, string>) => {
    // Implementar filtros no futuro
    console.log('Filter:', filters);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Gestão de Clientes</h1>
          <p className="text-gray-600">Visualize e gerencie todos os clientes do banco</p>
        </div>
        <Button className="flex items-center gap-2" onClick={handleNewClient}>
          <Plus className="h-4 w-4" />
          Novo Cliente
        </Button>
      </div>

      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Pesquisar por nome, documento ou NIF..."
            className="pl-10"
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
        </div>
        <Button
          variant="outline"
          className="flex items-center gap-2"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4" />
          Filtros
        </Button>
        <Button variant="outline" onClick={handleSearch}>
          <Search className="h-4 w-4 mr-2" />
          Buscar
        </Button>
        <Button variant="outline" onClick={() => loadClients()}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Atualizar
        </Button>
        <Button variant="outline" onClick={handleExport}>
          <Download className="h-4 w-4 mr-2" />
          Exportar
        </Button>
      </div>

      {/* Filtros Avançados */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle>Filtros Avançados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Tipo de Cliente</label>
                <Select
                  value={filters.client_type || 'all'}
                  onValueChange={(value) => setFilters(prev => ({
                    ...prev,
                    client_type: value === 'all' ? undefined : value as 'individual' | 'company'
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todos os tipos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os tipos</SelectItem>
                    <SelectItem value="individual">Individual</SelectItem>
                    <SelectItem value="company">Empresa</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <Select
                  value={filters.status || 'all'}
                  onValueChange={(value) => setFilters(prev => ({
                    ...prev,
                    status: value === 'all' ? undefined : value as 'active' | 'inactive' | 'blocked'
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todos os status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos os status</SelectItem>
                    <SelectItem value="active">Ativo</SelectItem>
                    <SelectItem value="inactive">Inativo</SelectItem>
                    <SelectItem value="blocked">Bloqueado</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Data Início</label>
                <Input
                  type="date"
                  value={filters.start_date}
                  onChange={(e) => setFilters(prev => ({ ...prev, start_date: e.target.value }))}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Data Fim</label>
                <Input
                  type="date"
                  value={filters.end_date}
                  onChange={(e) => setFilters(prev => ({ ...prev, end_date: e.target.value }))}
                />
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <Button onClick={handleSearch}>Aplicar Filtros</Button>
              <Button variant="outline" onClick={clearFilters}>Limpar Filtros</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Estatísticas */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Clientes</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total_clients || 0}</div>
            <p className="text-xs text-muted-foreground">Todos os clientes registrados</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clientes Particulares</CardTitle>
            <User className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.individual_clients || 0}</div>
            <p className="text-xs text-muted-foreground">Clientes singulares</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Novos Este Mês</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.new_this_month || 0}</div>
            <p className="text-xs text-muted-foreground">Registrados este mês</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clientes Empresa</CardTitle>
            <Building2 className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.company_clients || 0}</div>
            <p className="text-xs text-muted-foreground">Contas Empresariais</p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Clientes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Lista de Clientes
            <span className="text-sm font-normal text-gray-500">
              {pagination.total_records} cliente{pagination.total_records !== 1 ? 's' : ''} encontrado{pagination.total_records !== 1 ? 's' : ''}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ClientDataTable
            clients={clients}
            loading={loading}
            pagination={pagination}
            onPageChange={handlePageChange}
            onSort={handleSort}
            onFilter={handleFilter}
            onViewClient={handleViewClient}
            onEditClient={handleEditClient}
            onDeleteClient={handleDeleteClient}
          />
        </CardContent>
      </Card>

      {/* Modals */}
      <ClientViewModal
        client={selectedClient}
        isOpen={showViewModal}
        onClose={handleCloseModals}
      />

      <ClientEditModal
        client={selectedClient}
        isOpen={showEditModal}
        onClose={handleCloseModals}
        onClientUpdated={() => {
          loadClients();
          loadStats();
        }}
      />

      <NewClientModal
        isOpen={showNewClientModal}
        onClose={handleCloseModals}
      />
    </div>
  );
};

export default GestaoClientes;
