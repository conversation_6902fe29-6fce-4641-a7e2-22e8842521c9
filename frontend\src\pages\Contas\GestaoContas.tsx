import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { accountService, Account, AccountFilters } from '@/services/accountService';
import { branchService } from '@/services/branchService';
import AccountTypeSelectionModal from '@/components/modals/AccountTypeSelectionModal';
import AddSecondHolderModal from '@/components/modals/AddSecondHolderModal';
import EditAccountModal from '@/components/modals/EditAccountModal';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Lock,
  Unlock,
  X,
  Plus,
  RefreshCw,
  CreditCard,
  TrendingUp,
  Users,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  UserPlus
} from 'lucide-react';

interface Branch {
  id: number;
  name: string;
  code: string;
}

const GestaoContas = () => {
  const { toast } = useToast();
  const { hasPermission } = useAuth();

  // Estados principais
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [showAccountModal, setShowAccountModal] = useState(false);
  const [showAccountTypeModal, setShowAccountTypeModal] = useState(false);
  const [showEditAccountModal, setShowEditAccountModal] = useState(false);
  const [showSecondHolderModal, setShowSecondHolderModal] = useState(false);
  const [selectedAccountForHolder, setSelectedAccountForHolder] = useState<Account | null>(null);

  // Estados de filtros
  const [filters, setFilters] = useState<AccountFilters>({
    search: '',
    account_type: '',
    status: 'active', // Filtrar apenas contas ativas por padrão
    branch_id: undefined,
    page: 1,
    limit: 20
  });

  // Estados de paginação
  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_records: 0,
    records_per_page: 20,
    has_next: false,
    has_previous: false
  });

  // Carregar contas
  const loadAccounts = async () => {
    try {
      setLoading(true);
      const response = await accountService.getAccounts(filters);
      setAccounts(response.accounts);
      setPagination(response.pagination);
    } catch (error: any) {
      console.error('Erro ao carregar contas:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao carregar lista de contas",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar balcões
  const loadBranches = async () => {
    try {
      const response = await branchService.listBranches({
        page: 1,
        limit: 100,
        active: true
      });
      setBranches(response.branches);
    } catch (error) {
      console.error('Erro ao carregar balcões:', error);
    }
  };

  // Efeitos
  useEffect(() => {
    loadBranches();
  }, []);

  useEffect(() => {
    loadAccounts();
  }, [filters]);

  // Handlers de filtros
  const handleSearchChange = (value: string) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }));
  };

  const handleFilterChange = (key: keyof AccountFilters, value: any) => {
    // Converter valores "all" para string vazia para o backend
    const processedValue = value === 'all' ? '' : value;
    setFilters(prev => ({ ...prev, [key]: processedValue, page: 1 }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      account_type: '',
      status: 'active', // Manter filtro de contas ativas por padrão
      branch_id: undefined,
      page: 1,
      limit: 20
    });
  };

  // Handlers de ações
  const handleViewAccount = (account: Account) => {
    setSelectedAccount(account);
    setShowAccountModal(true);
  };

  const handleBlockAccount = async (account: Account) => {
    if (!hasPermission('accounts', 'block')) {
      toast({
        title: "Sem Permissão",
        description: "Não tem permissão para bloquear contas",
        variant: "destructive"
      });
      return;
    }

    try {
      await accountService.blockAccount(account.id);
      toast({
        title: "Sucesso",
        description: `Conta ${account.account_number} bloqueada com sucesso`,
      });
      loadAccounts();
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao bloquear conta",
        variant: "destructive"
      });
    }
  };

  const handleUnblockAccount = async (account: Account) => {
    if (!hasPermission('accounts', 'unblock')) {
      toast({
        title: "Sem Permissão",
        description: "Não tem permissão para desbloquear contas",
        variant: "destructive"
      });
      return;
    }

    try {
      await accountService.unblockAccount(account.id);
      toast({
        title: "Sucesso",
        description: `Conta ${account.account_number} desbloqueada com sucesso`,
      });
      loadAccounts();
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao desbloquear conta",
        variant: "destructive"
      });
    }
  };

  // Função para adicionar segundo titular
  const handleAddSecondHolder = (account: Account) => {
    setSelectedAccountForHolder(account);
    setShowSecondHolderModal(true);
  };

  // Função para obter cor do status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'blocked': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'inactive': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  // Função para obter ícone do status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4" />;
      case 'blocked': return <Lock className="h-4 w-4" />;
      case 'inactive': return <Clock className="h-4 w-4" />;
      case 'closed': return <X className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  // Função para formatar tipo de conta
  const formatAccountType = (type: string) => {
    const types: Record<string, string> = {
      'corrente': 'Conta Particular/Singular',
      'salario': 'Conta Salário',
      'junior': 'Conta Júnior (2 titular)'
    };
    return types[type] || type;
  };

  // Função para formatar valor
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <CreditCard className="h-8 w-8 text-twins-primary" />
            Gestão de Contas
          </h1>
          <p className="text-muted-foreground">
            Gerencie todas as contas bancárias do sistema
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={loadAccounts} disabled={loading} variant="outline">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
          {hasPermission('accounts', 'write') && (
            <>
              <Button onClick={() => setShowAccountTypeModal(true)} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Abrir Conta
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Contas</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pagination.total_records}</div>
            <p className="text-xs text-muted-foreground">
              Todas as contas registadas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contas Ativas</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {accounts.filter(acc => acc.status === 'active').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Contas em funcionamento
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contas Bloqueadas</CardTitle>
            <Lock className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {accounts.filter(acc => acc.status === 'blocked').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Contas temporariamente bloqueadas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo Total</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(accounts.reduce((sum, acc) => sum + acc.balance, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              Soma de todos os saldos
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros de Pesquisa
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Pesquisar</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="search"
                  placeholder="Número da conta, titular..."
                  value={filters.search}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Tipo de Conta</Label>
              <Select value={filters.account_type} onValueChange={(value) => handleFilterChange('account_type', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos os tipos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os tipos</SelectItem>
                  <SelectItem value="corrente">Conta Particular/Singular</SelectItem>
                  <SelectItem value="salario">Conta Salário</SelectItem>
                  <SelectItem value="junior">Conta Júnior (2 titular)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos os status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os status</SelectItem>
                  <SelectItem value="active">Ativa</SelectItem>
                  <SelectItem value="blocked">Bloqueada</SelectItem>
                  <SelectItem value="inactive">Inativa</SelectItem>
                  <SelectItem value="closed">Fechada</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Balcão</Label>
              <Select value={filters.branch_id?.toString()} onValueChange={(value) => handleFilterChange('branch_id', value ? parseInt(value) : undefined)}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos os balcões" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os balcões</SelectItem>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.id.toString()}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button onClick={clearFilters} variant="outline" className="w-full">
                Limpar Filtros
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Contas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Lista de Contas
            <span className="text-sm font-normal text-gray-500">
              {pagination.total_records} conta{pagination.total_records !== 1 ? 's' : ''} encontrada{pagination.total_records !== 1 ? 's' : ''}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-600">Carregando contas...</span>
            </div>
          ) : accounts.length === 0 ? (
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Nenhuma conta encontrada</p>
              <p className="text-sm text-gray-500">Tente ajustar os filtros de pesquisa</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium">Número da Conta</th>
                    <th className="text-left py-3 px-4 font-medium">Titular</th>
                    <th className="text-left py-3 px-4 font-medium">Tipo</th>
                    <th className="text-left py-3 px-4 font-medium">Saldo</th>
                    <th className="text-left py-3 px-4 font-medium">Status</th>
                    <th className="text-left py-3 px-4 font-medium">Balcão</th>
                    <th className="text-left py-3 px-4 font-medium">Abertura</th>
                    <th className="text-left py-3 px-4 font-medium">Ações</th>
                  </tr>
                </thead>
                <tbody>
                  {accounts.map((account) => (
                    <tr key={account.id} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="py-3 px-4">
                        <div className="font-mono text-sm">{account.account_number}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <div className="font-medium">
                            {account.holders && account.holders.length > 0
                              ? (account.holders[0].full_name || account.holders[0].company_name || account.holders[0].client_name || 'N/A')
                              : (account.primary_holder_name || account.primary_holder_company || 'N/A')
                            }
                          </div>
                          {/* Indicador de múltiplos titulares */}
                          {account.holders && account.holders.length > 1 && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Badge variant="secondary" className="flex items-center gap-1 text-xs">
                                  <Users className="h-3 w-3" />
                                  +{account.holders.length - 1}
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="space-y-1">
                                  <p className="font-medium">Titulares da conta:</p>
                                  {account.holders.map((holder, index) => (
                                    <p key={index} className="text-sm">
                                      • {holder.full_name || holder.company_name || holder.client_name}
                                      <span className="text-gray-400 ml-1">({holder.holder_type})</span>
                                    </p>
                                  ))}
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-sm">{formatAccountType(account.account_type)}</span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-medium">{formatCurrency(account.balance)}</div>
                        {account.available_balance !== account.balance && (
                          <div className="text-xs text-gray-500">
                            Disponível: {formatCurrency(account.available_balance)}
                          </div>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <Badge className={`${getStatusColor(account.status)} flex items-center gap-1 w-fit`}>
                          {getStatusIcon(account.status)}
                          {account.status === 'active' ? 'Ativa' :
                           account.status === 'blocked' ? 'Bloqueada' :
                           account.status === 'inactive' ? 'Inativa' : 'Fechada'}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-sm">{account.branch_name || 'N/A'}</span>
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-sm">
                          {new Date(account.opening_date).toLocaleDateString('pt-AO')}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleViewAccount(account)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Visualizar detalhes da conta</p>
                            </TooltipContent>
                          </Tooltip>

                          {hasPermission('accounts', 'write') && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    setSelectedAccount(account);
                                    setShowEditAccountModal(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Editar informações da conta</p>
                              </TooltipContent>
                            </Tooltip>
                          )}

                          {account.status === 'active' && hasPermission('accounts', 'block') && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleBlockAccount(account)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Lock className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Bloquear conta temporariamente</p>
                              </TooltipContent>
                            </Tooltip>
                          )}

                          {account.status === 'blocked' && hasPermission('accounts', 'unblock') && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleUnblockAccount(account)}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <Unlock className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Desbloquear conta</p>
                              </TooltipContent>
                            </Tooltip>
                          )}

                          {/* Botão Adicionar Segundo Titular - apenas para contas correntes sem segundo titular */}
                          {account.account_type === 'corrente' && account.status === 'active' && hasPermission('accounts', 'write') && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleAddSecondHolder(account)}
                                  className="text-blue-600 hover:text-blue-700"
                                >
                                  <UserPlus className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Adicionar segundo titular à conta</p>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Paginação */}
          {pagination.total_pages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Mostrando {((pagination.current_page - 1) * pagination.records_per_page) + 1} a{' '}
                {Math.min(pagination.current_page * pagination.records_per_page, pagination.total_records)} de{' '}
                {pagination.total_records} contas
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.has_previous}
                  onClick={() => handleFilterChange('page', pagination.current_page - 1)}
                >
                  Anterior
                </Button>
                <span className="text-sm">
                  Página {pagination.current_page} de {pagination.total_pages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!pagination.has_next}
                  onClick={() => handleFilterChange('page', pagination.current_page + 1)}
                >
                  Próxima
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modal de Visualização de Conta */}
      {showAccountModal && selectedAccount && (
        <AccountViewModal
          account={selectedAccount}
          isOpen={showAccountModal}
          onClose={() => {
            setShowAccountModal(false);
            setSelectedAccount(null);
          }}
        />
      )}

      {/* Modal de Seleção de Tipo de Conta */}
      <AccountTypeSelectionModal
        isOpen={showAccountTypeModal}
        onClose={() => setShowAccountTypeModal(false)}
      />

      {/* Modal de Edição de Conta */}
      <EditAccountModal
        isOpen={showEditAccountModal}
        onClose={() => {
          setShowEditAccountModal(false);
          setSelectedAccount(null);
        }}
        account={selectedAccount}
        onAccountUpdated={() => {
          loadAccounts();
          setShowEditAccountModal(false);
          setSelectedAccount(null);
        }}
      />

      {/* Modal de Adicionar Segundo Titular */}
      <AddSecondHolderModal
        isOpen={showSecondHolderModal}
        onClose={() => {
          setShowSecondHolderModal(false);
          setSelectedAccountForHolder(null);
        }}
        account={selectedAccountForHolder}
        onHolderAdded={() => {
          loadAccounts(); // Recarregar lista de contas
        }}
      />
    </div>
  );
};

// Componente Modal de Visualização de Conta
interface AccountViewModalProps {
  account: Account;
  isOpen: boolean;
  onClose: () => void;
}

const AccountViewModal: React.FC<AccountViewModalProps> = ({ account, isOpen, onClose }) => {
  if (!isOpen) return null;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  };

  const formatAccountType = (type: string) => {
    const types: Record<string, string> = {
      'corrente': 'Conta Particular/Singular',
      'salario': 'Conta Salário',
      'junior': 'Conta Júnior (2 titular)'
    };
    return types[type] || type;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">Detalhes da Conta</h2>
          <Button variant="outline" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-6">
          {/* Informações Básicas */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium text-gray-500">Número da Conta</Label>
              <p className="font-mono text-lg">{account.account_number}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-500">Tipo de Conta</Label>
              <p>{formatAccountType(account.account_type)}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-500">Status</Label>
              <p className="capitalize">{account.status}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-500">Data de Abertura</Label>
              <p>{new Date(account.opening_date).toLocaleDateString('pt-AO')}</p>
            </div>
          </div>

          {/* Saldos */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <Label className="text-sm font-medium text-blue-600">Saldo Atual</Label>
              <p className="text-2xl font-bold text-blue-700">{formatCurrency(account.balance)}</p>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <Label className="text-sm font-medium text-green-600">Saldo Disponível</Label>
              <p className="text-2xl font-bold text-green-700">{formatCurrency(account.available_balance)}</p>
            </div>
            <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
              <Label className="text-sm font-medium text-orange-600">Limite de Descoberto</Label>
              <p className="text-2xl font-bold text-orange-700">{formatCurrency(account.overdraft_limit)}</p>
            </div>
          </div>

          {/* Titulares */}
          {account.holders && account.holders.length > 0 && (
            <div>
              <Label className="text-sm font-medium text-gray-500 mb-2 block">Titulares da Conta</Label>
              <div className="space-y-2">
                {account.holders.map((holder, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div>
                      <p className="font-medium">
                        {holder.full_name || holder.company_name || holder.client_name || 'N/A'}
                      </p>
                      <p className="text-sm text-gray-500 capitalize">{holder.holder_type}</p>
                      {holder.document_number && (
                        <p className="text-xs text-gray-400">{holder.document_type}: {holder.document_number}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Informações Adicionais */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium text-gray-500">Balcão</Label>
              <p>{account.branch_name || 'N/A'}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-500">Criado em</Label>
              <p>{new Date(account.created_at).toLocaleDateString('pt-AO')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GestaoContas;
