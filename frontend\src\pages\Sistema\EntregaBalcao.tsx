import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { Building, Calculator, CheckCircle, AlertCircle, Loader2, Shield } from 'lucide-react';
import {
  treasuryService,
  CashDenominations,
  DeliverToCounterRequest
} from '@/services/treasuryService';

const EntregaBalcao = () => {
  const [formData, setFormData] = useState({
    counter_id: '',
    counter_name: '',
    branch_name: '',
    amount: '',
    notes: ''
  });

  const [denominations, setDenominations] = useState<CashDenominations>(
    treasuryService.getEmptyDenominations()
  );

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { toast } = useToast();

  // Dados dos balcões disponíveis
  const availableCounters = [
    { id: 'BAL001', name: 'Balcão Principal', branch: 'Agência Central' },
    { id: 'BAL002', name: 'Balcão Empresarial', branch: 'Agência Central' },
    { id: 'BAL003', name: 'Balcão Atendimento', branch: 'Agência Talatona' },
    { id: 'BAL004', name: 'Balcão VIP', branch: 'Agência Talatona' },
    { id: 'BAL005', name: 'Balcão Geral', branch: 'Agência Viana' },
    { id: 'BAL006', name: 'Balcão Express', branch: 'Agência Benfica' }
  ];

  // Validar formulário
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.counter_id) {
      newErrors.counter_id = 'Balcão é obrigatório';
    }

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Valor deve ser maior que zero';
    }

    // Validar se total das denominações corresponde ao valor
    const denominationsTotal = treasuryService.calculateDenominationsTotal(denominations);
    const amount = parseFloat(formData.amount);

    if (amount > 0 && Math.abs(denominationsTotal - amount) > 0.01) {
      newErrors.denominations = `Total das denominações (${treasuryService.formatCurrency(denominationsTotal)}) não confere com o valor informado (${treasuryService.formatCurrency(amount)})`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submeter formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);

      const deliveryData: DeliverToCounterRequest = {
        counter_id: formData.counter_id,
        counter_name: formData.counter_name,
        branch_name: formData.branch_name,
        amount: parseFloat(formData.amount),
        denominations,
        notes: formData.notes || undefined
      };

      await treasuryService.deliverToCounter(deliveryData);

      toast({
        title: "Sucesso",
        description: `Entrega de ${treasuryService.formatCurrency(parseFloat(formData.amount))} ao balcão ${formData.counter_name} realizada com sucesso`
      });

      // Limpar formulário
      setFormData({
        counter_id: '',
        counter_name: '',
        branch_name: '',
        amount: '',
        notes: ''
      });
      setDenominations(treasuryService.getEmptyDenominations());
      setErrors({});

    } catch (error: any) {
      console.error('Erro ao realizar entrega:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao realizar entrega ao balcão. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Atualizar dados do balcão selecionado
  const handleCounterChange = (counterId: string) => {
    const selectedCounter = availableCounters.find(c => c.id === counterId);
    if (selectedCounter) {
      setFormData(prev => ({
        ...prev,
        counter_id: counterId,
        counter_name: selectedCounter.name,
        branch_name: selectedCounter.branch
      }));
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Entrega ao Balcão</h1>
          <p className="text-gray-600 dark:text-gray-400">Registar entrega de valores para balcões da agência</p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Informações da Entrega
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
                <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300 mb-2">
                  <Shield className="h-4 w-4" />
                  <span className="font-medium">Entrega para Balcão</span>
                </div>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Esta operação registará a entrega de valores do cofre principal para um balcão específico.
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="counter_id">Balcão de Destino *</Label>
                  <Select value={formData.counter_id} onValueChange={handleCounterChange}>
                    <SelectTrigger className={errors.counter_id ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Selecione o balcão" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableCounters.map((counter) => (
                        <SelectItem key={counter.id} value={counter.id}>
                          {counter.name} - {counter.branch}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.counter_id && (
                    <p className="text-sm text-red-500 mt-1">{errors.counter_id}</p>
                  )}
                </div>

                {formData.branch_name && (
                  <div className="bg-gray-50 p-3 rounded border dark:bg-gray-700">
                    <div className="text-sm">
                      <div><strong>Balcão:</strong> {formData.counter_name}</div>
                      <div><strong>Agência:</strong> {formData.branch_name}</div>
                    </div>
                  </div>
                )}

                <div>
                  <Label htmlFor="amount">Valor Total a Entregar (AOA) *</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    placeholder="0,00"
                    value={formData.amount}
                    onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                    className={`text-lg font-bold ${errors.amount ? 'border-red-500' : ''}`}
                  />
                  {errors.amount && (
                    <p className="text-sm text-red-500 mt-1">{errors.amount}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="notes">Observações</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Observações sobre a entrega (opcional)"
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Denominações */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Contagem de Denominações
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Notas Grandes */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Notas Grandes</h4>
                {[
                  { key: 'notes_10000', label: 'Notas de 10.000 Kz', value: 10000 },
                  { key: 'notes_5000', label: 'Notas de 5.000 Kz', value: 5000 },
                  { key: 'notes_2000', label: 'Notas de 2.000 Kz', value: 2000 },
                  { key: 'notes_1000', label: 'Notas de 1.000 Kz', value: 1000 }
                ].map(({ key, label, value }) => (
                  <div key={key} className="flex items-center justify-between">
                    <span className="text-sm">{label}</span>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        min="0"
                        className="w-20 text-center"
                        value={denominations[key as keyof CashDenominations]}
                        onChange={(e) => setDenominations(prev => ({
                          ...prev,
                          [key]: parseInt(e.target.value) || 0
                        }))}
                      />
                      <span className="text-sm text-muted-foreground w-24">
                        {treasuryService.formatCurrency((denominations[key as keyof CashDenominations] || 0) * value)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Notas Pequenas */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Notas Pequenas</h4>
                {[
                  { key: 'notes_500', label: 'Notas de 500 Kz', value: 500 },
                  { key: 'notes_200', label: 'Notas de 200 Kz', value: 200 },
                  { key: 'notes_100', label: 'Notas de 100 Kz', value: 100 },
                  { key: 'notes_50', label: 'Notas de 50 Kz', value: 50 }
                ].map(({ key, label, value }) => (
                  <div key={key} className="flex items-center justify-between">
                    <span className="text-sm">{label}</span>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        min="0"
                        className="w-20 text-center"
                        value={denominations[key as keyof CashDenominations]}
                        onChange={(e) => setDenominations(prev => ({
                          ...prev,
                          [key]: parseInt(e.target.value) || 0
                        }))}
                      />
                      <span className="text-sm text-muted-foreground w-24">
                        {treasuryService.formatCurrency((denominations[key as keyof CashDenominations] || 0) * value)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Moedas */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Moedas</h4>
                {[
                  { key: 'coins_10', label: 'Moedas de 10 Kz', value: 10 },
                  { key: 'coins_5', label: 'Moedas de 5 Kz', value: 5 },
                  { key: 'coins_1', label: 'Moedas de 1 Kz', value: 1 }
                ].map(({ key, label, value }) => (
                  <div key={key} className="flex items-center justify-between">
                    <span className="text-sm">{label}</span>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        min="0"
                        className="w-20 text-center"
                        value={denominations[key as keyof CashDenominations]}
                        onChange={(e) => setDenominations(prev => ({
                          ...prev,
                          [key]: parseInt(e.target.value) || 0
                        }))}
                      />
                      <span className="text-sm text-muted-foreground w-24">
                        {treasuryService.formatCurrency((denominations[key as keyof CashDenominations] || 0) * value)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Total Calculado */}
              <div className="border-t pt-4">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total Calculado:</span>
                  <span className="text-lg font-bold">
                    {treasuryService.formatCurrency(treasuryService.calculateDenominationsTotal(denominations))}
                  </span>
                </div>
                {errors.denominations && (
                  <p className="text-sm text-red-500 mt-2">{errors.denominations}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Botão de Submissão */}
        <div className="flex justify-end mt-6">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex items-center gap-2 min-w-[200px]"
          >
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4" />
            )}
            {isSubmitting ? 'Processando...' : 'Confirmar Entrega'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default EntregaBalcao;
