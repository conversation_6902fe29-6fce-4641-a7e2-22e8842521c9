
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Banknote, MapPin, Battery, Wifi, RefreshCw, AlertTriangle } from 'lucide-react';
import { atmService, ATM, LoadATMRequest } from '@/services/atmService';
import { toast } from 'sonner';

const CarregamentoATM = () => {
  const [atms, setAtms] = useState<ATM[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedATM, setSelectedATM] = useState<ATM | null>(null);
  const [isLoadingModalOpen, setIsLoadingModalOpen] = useState(false);
  const [loadingForm, setLoadingForm] = useState({
    amount: '',
    notes: '',
    denominations: {
      notes_10000: 0,
      notes_5000: 0,
      notes_2000: 0,
      notes_1000: 0,
      notes_500: 0,
      notes_200: 0,
      notes_100: 0,
      notes_50: 0,
      coins_10: 0,
      coins_5: 0,
      coins_1: 0
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Carregar ATMs ao montar o componente
  useEffect(() => {
    loadATMs();
  }, []);

  const loadATMs = async () => {
    try {
      setLoading(true);
      const data = await atmService.getATMs();
      setAtms(data.atms);
    } catch (error) {
      console.error('Erro ao carregar ATMs:', error);
      toast.error('Erro ao carregar lista de ATMs');
    } finally {
      setLoading(false);
    }
  };

  // Calcular total das denominações
  const calculateDenominationsTotal = () => {
    const { denominations } = loadingForm;
    return (
      denominations.notes_10000 * 10000 +
      denominations.notes_5000 * 5000 +
      denominations.notes_2000 * 2000 +
      denominations.notes_1000 * 1000 +
      denominations.notes_500 * 500 +
      denominations.notes_200 * 200 +
      denominations.notes_100 * 100 +
      denominations.notes_50 * 50 +
      denominations.coins_10 * 10 +
      denominations.coins_5 * 5 +
      denominations.coins_1 * 1
    );
  };

  // Atualizar denominação
  const updateDenomination = (key: string, value: number) => {
    setLoadingForm(prev => ({
      ...prev,
      denominations: {
        ...prev.denominations,
        [key]: Math.max(0, value)
      }
    }));
  };

  // Abrir modal de carregamento
  const openLoadingModal = (atm: ATM) => {
    if (atm.status === 'offline') {
      toast.error('ATM está offline e não pode ser carregado');
      return;
    }
    setSelectedATM(atm);
    setIsLoadingModalOpen(true);
    // Reset form
    setLoadingForm({
      amount: '',
      notes: '',
      denominations: {
        notes_10000: 0,
        notes_5000: 0,
        notes_2000: 0,
        notes_1000: 0,
        notes_500: 0,
        notes_200: 0,
        notes_100: 0,
        notes_50: 0,
        coins_10: 0,
        coins_5: 0,
        coins_1: 0
      }
    });
  };

  // Submeter carregamento
  const handleLoadATM = async () => {
    if (!selectedATM) return;

    const amount = parseFloat(loadingForm.amount);
    const denominationsTotal = calculateDenominationsTotal();

    // Validações
    if (!amount || amount <= 0) {
      toast.error('Valor deve ser maior que zero');
      return;
    }

    if (Math.abs(denominationsTotal - amount) > 0.01) {
      toast.error('Total das denominações deve ser igual ao valor informado');
      return;
    }

    const newBalance = selectedATM.current_balance + amount;
    if (newBalance > selectedATM.cash_capacity) {
      toast.error('Carregamento excede a capacidade do ATM');
      return;
    }

    try {
      setIsSubmitting(true);

      const loadRequest: LoadATMRequest = {
        atm_id: selectedATM.id,
        amount,
        denominations: loadingForm.denominations,
        notes: loadingForm.notes || undefined
      };

      await atmService.loadATM(loadRequest);

      toast.success(`Carregamento de ${amount.toLocaleString('pt-AO')} AOA realizado com sucesso`);
      setIsLoadingModalOpen(false);
      loadATMs(); // Recarregar lista de ATMs

    } catch (error: any) {
      console.error('Erro ao carregar ATM:', error);
      toast.error(error.message || 'Erro ao realizar carregamento');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Carregando ATMs...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Carregamento ATM</h1>
          <p className="text-gray-600 dark:text-gray-400">Gestão e carregamento dos caixas automáticos</p>
        </div>
        <Button onClick={loadATMs} className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          Atualizar
        </Button>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {atms.map((atm) => (
          <Card key={atm.id}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                {atm.location}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span className={`font-bold ${atmService.getStatusColor(atm.status)}`}>
                    {atmService.getStatusText(atm.status)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Saldo Atual:</span>
                  <span className={`font-bold ${atm.status === 'low_balance' ? 'text-orange-600' : ''}`}>
                    {atmService.formatCurrency(atm.current_balance)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Capacidade:</span>
                  <span className="font-bold">{atmService.formatCurrency(atm.cash_capacity)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Ocupação:</span>
                  <span className="font-bold">{atm.capacity_percentage}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Conexão:</span>
                  <Wifi className={`h-4 w-4 ${atm.status === 'offline' ? 'text-red-600' : 'text-green-600'}`} />
                </div>
                <div className="flex justify-between items-center">
                  <span>Energia:</span>
                  <Battery className={`h-4 w-4 ${atm.status === 'offline' ? 'text-red-600' : 'text-green-600'}`} />
                </div>

                {atm.status === 'offline' ? (
                  <Button className="w-full mt-4" variant="destructive" disabled>
                    Manutenção
                  </Button>
                ) : (
                  <Button
                    className="w-full mt-4"
                    variant={atm.status === 'low_balance' ? 'default' : 'outline'}
                    onClick={() => openLoadingModal(atm)}
                  >
                    {atm.status === 'low_balance' && <AlertTriangle className="h-4 w-4 mr-2" />}
                    Carregar ATM
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}

      </div>

      <Card>
        <CardHeader>
          <CardTitle className="dark:text-gray-100">Histórico de Carregamentos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="font-medium dark:text-gray-100">ATM Principal - Carregamento</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Hoje, 09:30 por João Silva</div>
              </div>
              <div className="text-green-600 font-bold">+22.500.000 Kz</div>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="font-medium dark:text-gray-100">ATM Entrada - Carregamento</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Ontem, 16:45 por Maria Santos</div>
              </div>
              <div className="text-green-600 font-bold">+13.500.000 Kz</div>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="font-medium dark:text-gray-100">ATM Drive-Through - Manutenção</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">2 dias atrás por Técnico Externo</div>
              </div>
              <div className="text-red-600 font-bold">Offline</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modal de Carregamento */}
      <Dialog open={isLoadingModalOpen} onOpenChange={setIsLoadingModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Carregar {selectedATM?.location}
            </DialogTitle>
          </DialogHeader>

          {selectedATM && (
            <div className="space-y-6">
              {/* Informações do ATM */}
              <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Saldo Atual:</span>
                  <p className="font-bold">{atmService.formatCurrency(selectedATM.current_balance)}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Capacidade:</span>
                  <p className="font-bold">{atmService.formatCurrency(selectedATM.cash_capacity)}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Ocupação:</span>
                  <p className="font-bold">{selectedATM.capacity_percentage}%</p>
                </div>
                <div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Espaço Disponível:</span>
                  <p className="font-bold">{atmService.formatCurrency(selectedATM.cash_capacity - selectedATM.current_balance)}</p>
                </div>
              </div>

              {/* Formulário de Carregamento */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Informações Gerais */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="amount">Valor Total *</Label>
                    <Input
                      id="amount"
                      type="number"
                      value={loadingForm.amount}
                      onChange={(e) => setLoadingForm(prev => ({ ...prev, amount: e.target.value }))}
                      placeholder="0,00"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <Label htmlFor="notes">Observações</Label>
                    <Textarea
                      id="notes"
                      value={loadingForm.notes}
                      onChange={(e) => setLoadingForm(prev => ({ ...prev, notes: e.target.value }))}
                      placeholder="Observações sobre o carregamento..."
                      rows={3}
                    />
                  </div>

                  {/* Alerta de total calculado */}
                  {calculateDenominationsTotal() > 0 && (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Total Calculado:</strong> {atmService.formatCurrency(calculateDenominationsTotal())}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                {/* Denominações */}
                <div className="space-y-4">
                  <h4 className="font-semibold">Denominações a Carregar</h4>
                  <div className="space-y-3 max-h-80 overflow-y-auto">
                    {[
                      { key: 'notes_10000', label: '10.000 Kz', value: 10000 },
                      { key: 'notes_5000', label: '5.000 Kz', value: 5000 },
                      { key: 'notes_2000', label: '2.000 Kz', value: 2000 },
                      { key: 'notes_1000', label: '1.000 Kz', value: 1000 },
                      { key: 'notes_500', label: '500 Kz', value: 500 },
                      { key: 'notes_200', label: '200 Kz', value: 200 },
                      { key: 'notes_100', label: '100 Kz', value: 100 },
                      { key: 'notes_50', label: '50 Kz', value: 50 },
                      { key: 'coins_10', label: '10 Kz', value: 10 },
                      { key: 'coins_5', label: '5 Kz', value: 5 },
                      { key: 'coins_1', label: '1 Kz', value: 1 }
                    ].map(({ key, label, value }) => (
                      <div key={key} className="grid grid-cols-2 gap-2 items-center">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">{label}</span>
                          <Input
                            type="number"
                            min="0"
                            value={loadingForm.denominations[key as keyof typeof loadingForm.denominations]}
                            onChange={(e) => updateDenomination(key, parseInt(e.target.value) || 0)}
                            className="w-20 text-center"
                          />
                        </div>
                        <div className="text-right">
                          <span className="text-sm text-gray-600">
                            {atmService.formatCurrency(
                              (loadingForm.denominations[key as keyof typeof loadingForm.denominations] || 0) * value
                            )}
                          </span>
                        </div>
                      </div>
                    ))}

                    <div className="border-t pt-3 mt-3">
                      <div className="flex justify-between font-bold">
                        <span>Total das Denominações:</span>
                        <span>{atmService.formatCurrency(calculateDenominationsTotal())}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Botões */}
              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => setIsLoadingModalOpen(false)}
                  disabled={isSubmitting}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleLoadATM}
                  disabled={isSubmitting || !loadingForm.amount || calculateDenominationsTotal() === 0}
                >
                  {isSubmitting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Carregando...
                    </>
                  ) : (
                    <>
                      <Banknote className="h-4 w-4 mr-2" />
                      Confirmar Carregamento
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CarregamentoATM;
