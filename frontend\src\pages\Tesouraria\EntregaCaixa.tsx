
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { Coins, Calculator, CheckCircle, AlertCircle, RefreshCw, Loader2 } from 'lucide-react';
import {
  treasuryService,
  CashDenominations,
  DeliverToCashRequest
} from '@/services/treasuryService';
import {
  cashRegisterManagementService,
  CashRegister
} from '@/services/cashRegisterManagementService';

const EntregaCaixa = () => {
  const [formData, setFormData] = useState({
    cash_register_id: '',
    amount: '',
    notes: ''
  });

  const [denominations, setDenominations] = useState<CashDenominations>(
    treasuryService.getEmptyDenominations()
  );

  const [availableCashRegisters, setAvailableCashRegisters] = useState<CashRegister[]>([]);
  const [isLoadingCashRegisters, setIsLoadingCashRegisters] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { toast } = useToast();

  // Carregar caixas disponíveis ao montar componente
  useEffect(() => {
    loadAvailableCashRegisters();
  }, []);

  const loadAvailableCashRegisters = async () => {
    try {
      setIsLoadingCashRegisters(true);
      const cashRegisters = await cashRegisterManagementService.getAvailableCashRegisters();
      setAvailableCashRegisters(cashRegisters);
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao carregar caixas disponíveis",
        variant: "destructive"
      });
    } finally {
      setIsLoadingCashRegisters(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpar erro do campo
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleDenominationChange = (key: keyof CashDenominations, value: string) => {
    const numValue = parseInt(value) || 0;
    setDenominations(prev => ({ ...prev, [key]: numValue }));
  };

  const calculateTotal = () => {
    return treasuryService.calculateDenominationsTotal(denominations);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.cash_register_id) {
      newErrors.cash_register_id = 'Caixa é obrigatório';
    }

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Valor deve ser maior que zero';
    }

    const calculatedTotal = calculateTotal();
    if (calculatedTotal <= 0) {
      newErrors.denominations = 'Deve informar pelo menos uma denominação';
    }

    if (formData.amount && calculatedTotal > 0) {
      const validation = treasuryService.validateDenominations(denominations, parseFloat(formData.amount));
      if (!validation.isValid) {
        newErrors.denominations = validation.error!;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);

      const deliveryData: DeliverToCashRequest = {
        cash_register_id: parseInt(formData.cash_register_id),
        amount: parseFloat(formData.amount),
        denominations,
        notes: formData.notes || undefined
      };

      await treasuryService.deliverToCash(deliveryData);

      toast({
        title: "Sucesso",
        description: `Entrega de ${treasuryService.formatCurrency(parseFloat(formData.amount))} realizada com sucesso`
      });

      // Limpar formulário
      setFormData({ cash_register_id: '', amount: '', notes: '' });
      setDenominations(treasuryService.getEmptyDenominations());
      setErrors({});

    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao realizar entrega",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalCalculado = calculateTotal();
  const isFormValid = formData.cash_register_id && formData.amount && totalCalculado > 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Entrega a Caixa</h1>
          <p className="text-gray-600 dark:text-gray-400">Registar entrega de valores da tesouraria para o caixa</p>
        </div>
        <Button
          onClick={handleSubmit}
          disabled={!isFormValid || isSubmitting}
          className="flex items-center gap-2"
        >
          {isSubmitting ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <CheckCircle className="h-4 w-4" />
          )}
          {isSubmitting ? 'Processando...' : 'Confirmar Entrega'}
        </Button>
      </div>

      {totalCalculado > 0 && (
        <Alert className="border-green-200 bg-green-50">
          <Calculator className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Total Calculado:</strong> {treasuryService.formatCurrency(totalCalculado)}
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Informações da Entrega */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Coins className="h-5 w-5" />
                Informações da Entrega
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Caixa de Destino */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="cash_register_id">
                    Caixa de Destino <span className="text-red-500">*</span>
                  </Label>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={loadAvailableCashRegisters}
                    disabled={isLoadingCashRegisters}
                    className="h-6 px-2 text-xs"
                  >
                    <RefreshCw className={`h-3 w-3 mr-1 ${isLoadingCashRegisters ? 'animate-spin' : ''}`} />
                    Atualizar
                  </Button>
                </div>
                <Select value={formData.cash_register_id} onValueChange={(value) => handleInputChange('cash_register_id', value)}>
                  <SelectTrigger className={errors.cash_register_id ? 'border-red-500' : ''}>
                    <SelectValue placeholder={isLoadingCashRegisters ? "Carregando..." : "Selecione o caixa"} />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingCashRegisters ? (
                      <SelectItem value="loading" disabled>
                        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                        Carregando...
                      </SelectItem>
                    ) : availableCashRegisters.length === 0 ? (
                      <SelectItem value="empty" disabled>
                        Nenhum caixa disponível
                      </SelectItem>
                    ) : (
                      availableCashRegisters.map((cashRegister) => (
                        <SelectItem key={cashRegister.id} value={cashRegister.id.toString()}>
                          {cashRegister.register_number} - {cashRegister.branch_name}
                          {cashRegister.description && ` (${cashRegister.description})`}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {errors.cash_register_id && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.cash_register_id}
                  </p>
                )}
              </div>

              {/* Valor Total */}
              <div className="space-y-2">
                <Label htmlFor="amount">
                  Valor Total <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.amount}
                  onChange={(e) => handleInputChange('amount', e.target.value)}
                  placeholder="0.00"
                  className={errors.amount ? 'border-red-500' : ''}
                />
                {errors.amount && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.amount}
                  </p>
                )}
              </div>

              {/* Observações */}
              <div className="space-y-2">
                <Label htmlFor="notes">Observações</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Observações sobre a entrega..."
                  rows={3}
                />
              </div>

              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                <div className="flex items-center gap-2 text-blue-700 mb-2">
                  <Coins className="h-4 w-4" />
                  <span className="font-medium">Entrega da Tesouraria</span>
                </div>
                <p className="text-sm text-blue-600">
                  Esta operação registará a entrega de valores da tesouraria para o caixa selecionado.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Denominações */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Denominações a Entregar
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {treasuryService.getDenominationConfig().map((config) => (
                <div key={config.key} className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={config.key}>{config.label}</Label>
                    <Input
                      id={config.key}
                      type="number"
                      min="0"
                      value={denominations[config.key]}
                      onChange={(e) => handleDenominationChange(config.key, e.target.value)}
                      placeholder="Quantidade"
                      className="dark:bg-gray-700 dark:text-gray-100"
                    />
                  </div>
                  <div>
                    <Label className="dark:text-gray-100">Valor Total</Label>
                    <Input
                      value={treasuryService.formatCurrency(denominations[config.key] * config.value)}
                      readOnly
                      className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100"
                    />
                  </div>
                </div>
              ))}

              {errors.denominations && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.denominations}
                  </p>
                </div>
              )}

              <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-green-800">Total das Denominações:</span>
                  <span className="font-bold text-green-800 text-lg">
                    {treasuryService.formatCurrency(totalCalculado)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </form>
    </div>
  );
};

export default EntregaCaixa;
