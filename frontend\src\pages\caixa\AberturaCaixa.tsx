import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import {
  cashRegisterSessionService,
  CashDenominations,
  CashRegisterSession,
  OpenSessionRequest
} from '@/services/cashRegisterSessionService';
import { cashRegisterManagementService, CashRegister } from '@/services/cashRegisterManagementService';
import { userService, User } from '@/services/userService';
import { DoorO<PERSON>, Wallet, Calculator, CheckCircle, AlertCircle, Clock, User as UserIcon, RefreshCw } from 'lucide-react';

interface FormErrors {
  numeroCaixa?: string;
  operador?: string;
  saldoInicial?: string;
  observacoes?: string;
}

interface DenominacaoForm {
  notes_10000: number;
  notes_5000: number;
  notes_2000: number;
  notes_1000: number;
  notes_500: number;
  notes_200: number;
  notes_100: number;
  notes_50: number;
  coins_10: number;
  coins_5: number;
  coins_1: number;
}

const AberturaCaixa = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [currentSession, setCurrentSession] = useState<CashRegisterSession | null>(null);
  const [isCheckingSession, setIsCheckingSession] = useState(true);

  const [formData, setFormData] = useState({
    numeroCaixa: '',
    operador: user?.name || '',
    dataAbertura: new Date().toISOString().split('T')[0],
    horaAbertura: new Date().toLocaleTimeString('pt-AO', { hour: '2-digit', minute: '2-digit' }),
    saldoInicial: '',
    observacoes: ''
  });

  const [denominacoes, setDenominacoes] = useState<DenominacaoForm>({
    notes_10000: 0,
    notes_5000: 0,
    notes_2000: 0,
    notes_1000: 0,
    notes_500: 0,
    notes_200: 0,
    notes_100: 0,
    notes_50: 0,
    coins_10: 0,
    coins_5: 0,
    coins_1: 0
  });

  const [availableCashRegisters, setAvailableCashRegisters] = useState<CashRegister[]>([]);
  const [isLoadingCashRegisters, setIsLoadingCashRegisters] = useState(false);
  const [availableCashiers, setAvailableCashiers] = useState<User[]>([]);
  const [isLoadingCashiers, setIsLoadingCashiers] = useState(false);

  // Verificar se há sessão ativa e carregar dados ao carregar a página
  useEffect(() => {
    if (user) {
      checkCurrentSession();
      loadAvailableCashRegisters();
      loadAvailableCashiers();
    }
  }, [user]);

  const loadAvailableCashRegisters = async () => {
    try {
      setIsLoadingCashRegisters(true);
      const cashRegisters = await cashRegisterManagementService.getAvailableCashRegisters();
      setAvailableCashRegisters(cashRegisters);
    } catch (error: any) {
      console.error('Erro ao carregar caixas disponíveis:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao carregar caixas disponíveis",
        variant: "destructive"
      });
    } finally {
      setIsLoadingCashRegisters(false);
    }
  };

  const loadAvailableCashiers = async () => {
    try {
      setIsLoadingCashiers(true);
      const cashiers = await userService.getCashiers();
      setAvailableCashiers(cashiers);
    } catch (error: any) {
      console.error('Erro ao carregar operadores de caixa:', error);
      toast({
        title: "Erro",
        description: error.message || "Erro ao carregar operadores de caixa",
        variant: "destructive"
      });
    } finally {
      setIsLoadingCashiers(false);
    }
  };

  const checkCurrentSession = async () => {
    try {
      setIsCheckingSession(true);
      const session = await cashRegisterSessionService.getCurrentSession();
      setCurrentSession(session);
    } catch (error) {
      console.error('Erro ao verificar sessão atual:', error);
    } finally {
      setIsCheckingSession(false);
    }
  };

  const calcularTotal = () => {
    return cashRegisterSessionService.calculateDenominationsTotal(denominacoes);
  };

  const formatarMoeda = (valor: number) => {
    return cashRegisterSessionService.formatCurrency(valor);
  };

  const validarFormulario = () => {
    const newErrors: FormErrors = {};

    if (!formData.numeroCaixa) {
      newErrors.numeroCaixa = 'Número do caixa é obrigatório';
    }

    if (!formData.operador) {
      newErrors.operador = 'Operador é obrigatório';
    }

    const totalCalculado = calcularTotal();
    const saldoInformado = parseFloat(formData.saldoInicial) || 0;

    if (Math.abs(totalCalculado - saldoInformado) > 0.01) {
      newErrors.saldoInicial = 'Saldo inicial deve coincidir com o total das denominações';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleDenominacaoChange = (denominacao: keyof DenominacaoForm, value: string) => {
    const numericValue = parseInt(value) || 0;
    const novasDenominacoes = { ...denominacoes, [denominacao]: numericValue };
    setDenominacoes(novasDenominacoes);

    // Atualizar saldo inicial automaticamente
    const total = cashRegisterSessionService.calculateDenominationsTotal(novasDenominacoes);
    setFormData(prev => ({ ...prev, saldoInicial: total.toString() }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validarFormulario()) {
      toast({
        title: "Erro de validação",
        description: "Por favor, corrija os erros no formulário",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Preparar dados para envio
      const openSessionData: OpenSessionRequest = {
        cash_register_number: formData.numeroCaixa,
        opening_balance: parseFloat(formData.saldoInicial) || 0,
        denominations: denominacoes,
        notes: formData.observacoes || undefined
      };

      // Abrir sessão de caixa via API
      const session = await cashRegisterSessionService.openSession(openSessionData);
      setCurrentSession(session);

      toast({
        title: "Caixa aberto com sucesso",
        description: `Caixa ${formData.numeroCaixa} foi aberto com saldo inicial de ${formatarMoeda(calcularTotal())}`,
      });

      // Limpar formulário após sucesso
      setFormData({
        numeroCaixa: '',
        operador: user?.name || '',
        dataAbertura: new Date().toISOString().split('T')[0],
        horaAbertura: new Date().toLocaleTimeString('pt-AO', { hour: '2-digit', minute: '2-digit' }),
        saldoInicial: '',
        observacoes: ''
      });
      setDenominacoes({
        notes_10000: 0, notes_5000: 0, notes_2000: 0, notes_1000: 0,
        notes_500: 0, notes_200: 0, notes_100: 0, notes_50: 0,
        coins_10: 0, coins_5: 0, coins_1: 0
      });
      setErrors({});

    } catch (error: any) {
      console.error('Erro ao abrir caixa:', error);
      toast({
        title: "Erro",
        description: error.message || "Ocorreu um erro ao abrir o caixa. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalCalculado = calcularTotal();
  const isFormValid = formData.numeroCaixa && formData.operador && totalCalculado > 0;

  // Mostrar loading enquanto verifica sessão
  if (isCheckingSession) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <RefreshCw className="h-5 w-5 animate-spin" />
          <span>Verificando sessão atual...</span>
        </div>
      </div>
    );
  }

  // Se já há uma sessão ativa, mostrar informações
  if (currentSession) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <CheckCircle className="h-8 w-8 text-green-600" />
              Caixa Já Aberto
            </h1>
            <p className="text-gray-600 dark:text-gray-400">Você já possui uma sessão de caixa ativa</p>
          </div>
          <Button onClick={checkCurrentSession} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Atualizar
          </Button>
        </div>

        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <div className="space-y-2">
              <div><strong>Caixa:</strong> {currentSession.cash_register_number}</div>
              <div><strong>Saldo Inicial:</strong> {formatarMoeda(currentSession.opening_balance)}</div>
              <div><strong>Saldo Atual:</strong> {formatarMoeda(currentSession.current_balance)}</div>
              <div><strong>Aberto em:</strong> {new Date(currentSession.opened_at).toLocaleString('pt-AO')}</div>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <DoorOpen className="h-8 w-8 text-twins-primary" />
            Abertura do Caixa
          </h1>
          <p className="text-gray-600 dark:text-gray-400">Registar abertura de caixa com contagem inicial de valores</p>
        </div>
        <Button
          onClick={handleSubmit}
          disabled={!isFormValid || isSubmitting}
          className="flex items-center gap-2"
        >
          <CheckCircle className="h-4 w-4" />
          {isSubmitting ? 'Abrindo...' : 'Confirmar Abertura'}
        </Button>
      </div>

      {totalCalculado > 0 && (
        <Alert className="border-green-200 bg-green-50">
          <Calculator className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Total Calculado:</strong> {formatarMoeda(totalCalculado)}
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Informações Básicas */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserIcon className="h-5 w-5" />
                Informações da Abertura
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Número do Caixa */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="numeroCaixa">
                    Número do Caixa <span className="text-red-500">*</span>
                  </Label>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={loadAvailableCashRegisters}
                    disabled={isLoadingCashRegisters}
                    className="h-6 px-2 text-xs"
                  >
                    <RefreshCw className={`h-3 w-3 mr-1 ${isLoadingCashRegisters ? 'animate-spin' : ''}`} />
                    Atualizar
                  </Button>
                </div>
                <Select value={formData.numeroCaixa} onValueChange={(value) => handleInputChange('numeroCaixa', value)}>
                  <SelectTrigger className={errors.numeroCaixa ? 'border-red-500' : ''}>
                    <SelectValue placeholder={isLoadingCashRegisters ? "Carregando caixas..." : "Selecione o caixa"} />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingCashRegisters ? (
                      <SelectItem value="loading" disabled>
                        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                        Carregando...
                      </SelectItem>
                    ) : availableCashRegisters.length === 0 ? (
                      <SelectItem value="empty" disabled>
                        Nenhum caixa disponível
                      </SelectItem>
                    ) : (
                      availableCashRegisters.map((cashRegister) => (
                        <SelectItem key={cashRegister.id} value={cashRegister.register_number}>
                          {cashRegister.register_number} - {cashRegister.branch_name}
                          {cashRegister.description && ` (${cashRegister.description})`}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {errors.numeroCaixa && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.numeroCaixa}
                  </p>
                )}
              </div>

              {/* Operador */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="operador">
                    Operador <span className="text-red-500">*</span>
                  </Label>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={loadAvailableCashiers}
                    disabled={isLoadingCashiers}
                    className="h-6 px-2 text-xs"
                  >
                    <RefreshCw className={`h-3 w-3 mr-1 ${isLoadingCashiers ? 'animate-spin' : ''}`} />
                    Atualizar
                  </Button>
                </div>
                <Select value={formData.operador} onValueChange={(value) => handleInputChange('operador', value)}>
                  <SelectTrigger className={errors.operador ? 'border-red-500' : ''}>
                    <SelectValue placeholder={isLoadingCashiers ? "Carregando operadores..." : "Selecione o operador"} />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingCashiers ? (
                      <SelectItem value="loading" disabled>
                        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                        Carregando...
                      </SelectItem>
                    ) : availableCashiers.length === 0 ? (
                      <SelectItem value="empty" disabled>
                        Nenhum operador disponível
                      </SelectItem>
                    ) : (
                      availableCashiers.map((cashier) => (
                        <SelectItem key={cashier.id} value={cashier.full_name}>
                          {cashier.full_name}
                          {cashier.branch_name && ` - ${cashier.branch_name}`}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {errors.operador && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.operador}
                  </p>
                )}
              </div>

              {/* Data e Hora */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="dataAbertura" className="dark:text-gray-100">Data de Abertura</Label>
                  <Input
                    id="dataAbertura"
                    type="date"
                    value={formData.dataAbertura}
                    onChange={(e) => handleInputChange('dataAbertura', e.target.value)}
                    className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100"
                    readOnly
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="horaAbertura" className="dark:text-gray-100">Hora de Abertura</Label>
                  <Input
                    id="horaAbertura"
                    value={formData.horaAbertura}
                    className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100"
                    readOnly
                  />
                </div>
              </div>

              {/* Saldo Inicial */}
              <div className="space-y-2">
                <Label htmlFor="saldoInicial" className="dark:text-gray-100">
                  Saldo Inicial (Kz) <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="saldoInicial"
                  type="number"
                  step="0.01"
                  value={formData.saldoInicial}
                  onChange={(e) => handleInputChange('saldoInicial', e.target.value)}
                  placeholder="0,00"
                  className={`${errors.saldoInicial ? 'border-red-500' : ''} bg-gray-50 dark:bg-gray-700 dark:text-gray-100`}
                  readOnly
                />
                {errors.saldoInicial && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.saldoInicial}
                  </p>
                )}
              </div>

              {/* Observações */}
              <div className="space-y-2">
                <Label htmlFor="observacoes">Observações</Label>
                <Input
                  id="observacoes"
                  value={formData.observacoes}
                  onChange={(e) => handleInputChange('observacoes', e.target.value)}
                  placeholder="Observações adicionais (opcional)"
                />
              </div>
            </CardContent>
          </Card>

          {/* Denominações */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wallet className="h-5 w-5" />
                Contagem de Denominações
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Notas Grandes */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Notas Grandes</h4>
                {[
                  { key: 'notes_10000', label: 'Notas de 10.000 Kz', valor: 10000 },
                  { key: 'notes_5000', label: 'Notas de 5.000 Kz', valor: 5000 },
                  { key: 'notes_2000', label: 'Notas de 2.000 Kz', valor: 2000 },
                  { key: 'notes_1000', label: 'Notas de 1.000 Kz', valor: 1000 }
                ].map(({ key, label, valor }) => (
                  <div key={key} className="grid grid-cols-3 gap-2 items-center">
                    <Label className="text-xs">{label}</Label>
                    <Input
                      type="number"
                      min="0"
                      value={denominacoes[key as keyof DenominacaoForm]}
                      onChange={(e) => handleDenominacaoChange(key as keyof DenominacaoForm, e.target.value)}
                      placeholder="Qtd"
                      className="text-center"
                    />
                    <span className="text-xs text-gray-600 text-right">
                      {formatarMoeda(denominacoes[key as keyof DenominacaoForm] * valor)}
                    </span>
                  </div>
                ))}
              </div>

              {/* Notas Pequenas */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Notas Pequenas</h4>
                {[
                  { key: 'notes_500', label: 'Notas de 500 Kz', valor: 500 },
                  { key: 'notes_200', label: 'Notas de 200 Kz', valor: 200 },
                  { key: 'notes_100', label: 'Notas de 100 Kz', valor: 100 },
                  { key: 'notes_50', label: 'Notas de 50 Kz', valor: 50 }
                ].map(({ key, label, valor }) => (
                  <div key={key} className="grid grid-cols-3 gap-2 items-center">
                    <Label className="text-xs">{label}</Label>
                    <Input
                      type="number"
                      min="0"
                      value={denominacoes[key as keyof DenominacaoForm]}
                      onChange={(e) => handleDenominacaoChange(key as keyof DenominacaoForm, e.target.value)}
                      placeholder="Qtd"
                      className="text-center"
                    />
                    <span className="text-xs text-gray-600 text-right">
                      {formatarMoeda(denominacoes[key as keyof DenominacaoForm] * valor)}
                    </span>
                  </div>
                ))}
              </div>

              {/* Moedas */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Moedas</h4>
                {[
                  { key: 'coins_10', label: 'Moedas de 10 Kz', valor: 10 },
                  { key: 'coins_5', label: 'Moedas de 5 Kz', valor: 5 },
                  { key: 'coins_1', label: 'Moedas de 1 Kz', valor: 1 }
                ].map(({ key, label, valor }) => (
                  <div key={key} className="grid grid-cols-3 gap-2 items-center">
                    <Label className="text-xs">{label}</Label>
                    <Input
                      type="number"
                      min="0"
                      value={denominacoes[key as keyof DenominacaoForm]}
                      onChange={(e) => handleDenominacaoChange(key as keyof DenominacaoForm, e.target.value)}
                      placeholder="Qtd"
                      className="text-center"
                    />
                    <span className="text-xs text-gray-600 text-right">
                      {formatarMoeda(denominacoes[key as keyof DenominacaoForm] * valor)}
                    </span>
                  </div>
                ))}
              </div>

              {/* Total */}
              <div className="pt-4 border-t">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total:</span>
                  <span className="text-lg font-bold text-twins-primary">
                    {formatarMoeda(totalCalculado)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </form>
    </div>
  );
};

export default AberturaCaixa;
