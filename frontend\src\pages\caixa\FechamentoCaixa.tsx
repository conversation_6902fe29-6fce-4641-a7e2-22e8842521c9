import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import {
  cashRegisterSessionService,
  CashDenominations,
  CashRegisterSession,
  CloseSessionRequest
} from '@/services/cashRegisterSessionService';
import { DoorClosed, Wallet, Calculator, CheckCircle, AlertCircle, Clock, User as UserIcon, RefreshCw, XCircle } from 'lucide-react';

interface FormErrors {
  saldoFinal?: string;
  observacoes?: string;
}

interface DenominacaoForm {
  notes_10000: number;
  notes_5000: number;
  notes_2000: number;
  notes_1000: number;
  notes_500: number;
  notes_200: number;
  notes_100: number;
  notes_50: number;
  coins_10: number;
  coins_5: number;
  coins_1: number;
}

const FechamentoCaixa = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [currentSession, setCurrentSession] = useState<CashRegisterSession | null>(null);
  const [isCheckingSession, setIsCheckingSession] = useState(true);

  const [formData, setFormData] = useState({
    saldoFinal: '',
    observacoes: ''
  });

  const [denominacoes, setDenominacoes] = useState<DenominacaoForm>({
    notes_10000: 0,
    notes_5000: 0,
    notes_2000: 0,
    notes_1000: 0,
    notes_500: 0,
    notes_200: 0,
    notes_100: 0,
    notes_50: 0,
    coins_10: 0,
    coins_5: 0,
    coins_1: 0
  });

  // Verificar se há sessão ativa ao carregar a página
  useEffect(() => {
    if (user) {
      checkCurrentSession();
    }
  }, [user]);

  const checkCurrentSession = async () => {
    try {
      setIsCheckingSession(true);
      const session = await cashRegisterSessionService.getCurrentSession();
      setCurrentSession(session);
    } catch (error) {
      console.error('Erro ao verificar sessão atual:', error);
      setCurrentSession(null);
    } finally {
      setIsCheckingSession(false);
    }
  };

  const calcularTotal = () => {
    return cashRegisterSessionService.calculateDenominationsTotal(denominacoes);
  };

  const formatarMoeda = (valor: number) => {
    return cashRegisterSessionService.formatCurrency(valor);
  };

  const formatDuration = (openedAt: string) => {
    const start = new Date(openedAt);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m`;
    }
    return `${diffMinutes}m`;
  };

  const validarFormulario = () => {
    const newErrors: FormErrors = {};

    const totalCalculado = calcularTotal();
    const saldoInformado = parseFloat(formData.saldoFinal) || 0;

    if (Math.abs(totalCalculado - saldoInformado) > 0.01) {
      newErrors.saldoFinal = 'Saldo final deve coincidir com o total das denominações';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleDenominacaoChange = (denominacao: keyof DenominacaoForm, value: string) => {
    const numericValue = parseInt(value) || 0;
    const novasDenominacoes = { ...denominacoes, [denominacao]: numericValue };
    setDenominacoes(novasDenominacoes);

    // Atualizar saldo final automaticamente
    const total = cashRegisterSessionService.calculateDenominationsTotal(novasDenominacoes);
    setFormData(prev => ({ ...prev, saldoFinal: total.toString() }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentSession) {
      toast({
        title: "Erro",
        description: "Nenhuma sessão ativa encontrada",
        variant: "destructive"
      });
      return;
    }

    if (!validarFormulario()) {
      toast({
        title: "Erro de validação",
        description: "Por favor, corrija os erros no formulário",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Preparar dados para envio
      const closeSessionData: CloseSessionRequest = {
        closing_balance: parseFloat(formData.saldoFinal) || 0,
        denominations: denominacoes,
        notes: formData.observacoes || undefined
      };

      // Fechar sessão de caixa via API
      await cashRegisterSessionService.closeSession(closeSessionData);
      setCurrentSession(null);

      toast({
        title: "Caixa fechado com sucesso",
        description: `Sessão encerrada com saldo final de ${formatarMoeda(calcularTotal())}`,
      });

      // Limpar formulário após sucesso
      setFormData({
        saldoFinal: '',
        observacoes: ''
      });
      setDenominacoes({
        notes_10000: 0, notes_5000: 0, notes_2000: 0, notes_1000: 0,
        notes_500: 0, notes_200: 0, notes_100: 0, notes_50: 0,
        coins_10: 0, coins_5: 0, coins_1: 0
      });
      setErrors({});

    } catch (error: any) {
      console.error('Erro ao fechar caixa:', error);
      toast({
        title: "Erro",
        description: error.message || "Ocorreu um erro ao fechar o caixa. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalCalculado = calcularTotal();
  const isFormValid = currentSession && totalCalculado >= 0;

  // Mostrar loading enquanto verifica sessão
  if (isCheckingSession) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <RefreshCw className="h-5 w-5 animate-spin" />
          <span>Verificando sessão atual...</span>
        </div>
      </div>
    );
  }

  // Se não há sessão ativa, mostrar mensagem
  if (!currentSession) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <XCircle className="h-8 w-8 text-red-600" />
              Nenhuma Sessão Ativa
            </h1>
            <p className="text-gray-600 dark:text-gray-400">Não há sessão de caixa ativa para fechar</p>
          </div>
          <Button onClick={checkCurrentSession} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Verificar Novamente
          </Button>
        </div>

        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertCircle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            Para fechar uma sessão de caixa, você precisa primeiro ter uma sessão ativa. 
            Abra uma sessão através da página de "Abertura de Caixa".
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <DoorClosed className="h-8 w-8 text-twins-primary" />
            Fechamento do Caixa
          </h1>
          <p className="text-gray-600 dark:text-gray-400">Registar fechamento de caixa com contagem final de valores</p>
        </div>
        <Button
          onClick={handleSubmit}
          disabled={!isFormValid || isSubmitting}
          className="flex items-center gap-2"
        >
          <CheckCircle className="h-4 w-4" />
          {isSubmitting ? 'Fechando...' : 'Confirmar Fechamento'}
        </Button>
      </div>

      {/* Informações da Sessão Atual */}
      <Alert className="border-blue-200 bg-blue-50">
        <CheckCircle className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div><strong>Caixa:</strong> {currentSession.cash_register_number}</div>
            <div><strong>Saldo Inicial:</strong> {formatarMoeda(currentSession.opening_balance)}</div>
            <div><strong>Saldo Atual:</strong> {formatarMoeda(currentSession.current_balance)}</div>
            <div><strong>Tempo Ativo:</strong> {formatDuration(currentSession.opened_at)}</div>
          </div>
        </AlertDescription>
      </Alert>

      {totalCalculado > 0 && (
        <Alert className="border-green-200 bg-green-50">
          <Calculator className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Total Calculado:</strong> {formatarMoeda(totalCalculado)}
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Informações do Fechamento */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserIcon className="h-5 w-5" />
                Informações do Fechamento
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Saldo Final */}
              <div className="space-y-2">
                <Label htmlFor="saldoFinal" className="dark:text-gray-100">
                  Saldo Final (Kz) <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="saldoFinal"
                  type="number"
                  step="0.01"
                  value={formData.saldoFinal}
                  onChange={(e) => handleInputChange('saldoFinal', e.target.value)}
                  placeholder="0,00"
                  className={`${errors.saldoFinal ? 'border-red-500' : ''} bg-gray-50 dark:bg-gray-700 dark:text-gray-100`}
                  readOnly
                />
                {errors.saldoFinal && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.saldoFinal}
                  </p>
                )}
              </div>

              {/* Diferença */}
              <div className="space-y-2">
                <Label className="dark:text-gray-100">Diferença</Label>
                <div className={`p-3 rounded border ${
                  (totalCalculado - currentSession.current_balance) === 0 
                    ? 'bg-green-50 border-green-200 text-green-800'
                    : (totalCalculado - currentSession.current_balance) > 0
                    ? 'bg-blue-50 border-blue-200 text-blue-800'
                    : 'bg-red-50 border-red-200 text-red-800'
                }`}>
                  <div className="font-medium">
                    {(totalCalculado - currentSession.current_balance) === 0 && 'Conferido ✓'}
                    {(totalCalculado - currentSession.current_balance) > 0 && 'Sobra: '}
                    {(totalCalculado - currentSession.current_balance) < 0 && 'Falta: '}
                    {(totalCalculado - currentSession.current_balance) !== 0 && 
                      formatarMoeda(Math.abs(totalCalculado - currentSession.current_balance))
                    }
                  </div>
                </div>
              </div>

              {/* Observações */}
              <div className="space-y-2">
                <Label htmlFor="observacoes">Observações</Label>
                <Input
                  id="observacoes"
                  value={formData.observacoes}
                  onChange={(e) => handleInputChange('observacoes', e.target.value)}
                  placeholder="Observações sobre o fechamento (opcional)"
                />
              </div>
            </CardContent>
          </Card>

          {/* Denominações */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wallet className="h-5 w-5" />
                Contagem Final de Denominações
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Notas Grandes */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Notas Grandes</h4>
                {[
                  { key: 'notes_10000', label: 'Notas de 10.000 Kz', valor: 10000 },
                  { key: 'notes_5000', label: 'Notas de 5.000 Kz', valor: 5000 },
                  { key: 'notes_2000', label: 'Notas de 2.000 Kz', valor: 2000 },
                  { key: 'notes_1000', label: 'Notas de 1.000 Kz', valor: 1000 }
                ].map(({ key, label, valor }) => (
                  <div key={key} className="grid grid-cols-3 gap-2 items-center">
                    <Label className="text-xs">{label}</Label>
                    <Input
                      type="number"
                      min="0"
                      value={denominacoes[key as keyof DenominacaoForm]}
                      onChange={(e) => handleDenominacaoChange(key as keyof DenominacaoForm, e.target.value)}
                      placeholder="Qtd"
                      className="text-center"
                    />
                    <span className="text-xs text-gray-600 text-right">
                      {formatarMoeda(denominacoes[key as keyof DenominacaoForm] * valor)}
                    </span>
                  </div>
                ))}
              </div>

              {/* Notas Pequenas */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Notas Pequenas</h4>
                {[
                  { key: 'notes_500', label: 'Notas de 500 Kz', valor: 500 },
                  { key: 'notes_200', label: 'Notas de 200 Kz', valor: 200 },
                  { key: 'notes_100', label: 'Notas de 100 Kz', valor: 100 },
                  { key: 'notes_50', label: 'Notas de 50 Kz', valor: 50 }
                ].map(({ key, label, valor }) => (
                  <div key={key} className="grid grid-cols-3 gap-2 items-center">
                    <Label className="text-xs">{label}</Label>
                    <Input
                      type="number"
                      min="0"
                      value={denominacoes[key as keyof DenominacaoForm]}
                      onChange={(e) => handleDenominacaoChange(key as keyof DenominacaoForm, e.target.value)}
                      placeholder="Qtd"
                      className="text-center"
                    />
                    <span className="text-xs text-gray-600 text-right">
                      {formatarMoeda(denominacoes[key as keyof DenominacaoForm] * valor)}
                    </span>
                  </div>
                ))}
              </div>

              {/* Moedas */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Moedas</h4>
                {[
                  { key: 'coins_10', label: 'Moedas de 10 Kz', valor: 10 },
                  { key: 'coins_5', label: 'Moedas de 5 Kz', valor: 5 },
                  { key: 'coins_1', label: 'Moedas de 1 Kz', valor: 1 }
                ].map(({ key, label, valor }) => (
                  <div key={key} className="grid grid-cols-3 gap-2 items-center">
                    <Label className="text-xs">{label}</Label>
                    <Input
                      type="number"
                      min="0"
                      value={denominacoes[key as keyof DenominacaoForm]}
                      onChange={(e) => handleDenominacaoChange(key as keyof DenominacaoForm, e.target.value)}
                      placeholder="Qtd"
                      className="text-center"
                    />
                    <span className="text-xs text-gray-600 text-right">
                      {formatarMoeda(denominacoes[key as keyof DenominacaoForm] * valor)}
                    </span>
                  </div>
                ))}
              </div>

              {/* Total */}
              <div className="pt-4 border-t">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total:</span>
                  <span className="text-lg font-bold text-twins-primary">
                    {formatarMoeda(totalCalculado)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </form>
    </div>
  );
};

export default FechamentoCaixa;
