import { makeRequest } from '@/utils/api';

// Interfaces para operações de tesouraria
export interface CashDenominations {
  notes_10000: number;
  notes_5000: number;
  notes_2000: number;
  notes_1000: number;
  notes_500: number;
  notes_200: number;
  notes_100: number;
  notes_50: number;
  coins_10: number;
  coins_5: number;
  coins_1: number;
}

export interface DeliverToCashRequest {
  cash_register_id: number;
  amount: number;
  denominations: CashDenominations;
  notes?: string;
}

export interface DeliverToVaultRequest {
  amount: number;
  source_type: 'cash_register' | 'treasury' | 'manual' | 'system';
  source_id?: string;
  denominations: CashDenominations;
  notes?: string;
}

export interface DeliverToTreasurerRequest {
  treasurer_id: string;
  amount: number;
  denominations: CashDenominations;
  notes?: string;
}

export interface DeliverToCounterRequest {
  counter_id: string;
  counter_name: string;
  branch_name: string;
  amount: number;
  denominations: CashDenominations;
  notes?: string;
}

export interface TreasuryDelivery {
  id: string;
  cash_register_id?: number;
  vault_id?: number;
  delivered_by: string;
  delivered_by_name: string;
  amount: number;
  denominations: CashDenominations;
  notes?: string;
  delivery_type: 'cash_delivery' | 'vault_delivery';
  status: 'pending' | 'confirmed' | 'cancelled';
  confirmed_by?: string;
  confirmed_at?: string;
  created_at: string;
  register_number?: string;
  cash_register_description?: string;
}

export interface VaultMovement {
  id: string;
  vault_id: number;
  movement_type: 'initial_balance' | 'deposit' | 'withdrawal' | 'transfer_in' | 'transfer_out' | 'adjustment';
  amount: number;
  previous_balance: number;
  new_balance: number;
  denominations: CashDenominations;
  reference_number: string;
  description: string;
  notes?: string;
  source_type?: 'cash_register' | 'treasury' | 'atm' | 'manual' | 'system';
  source_id?: string;
  destination_type?: 'cash_register' | 'treasury' | 'atm' | 'manual' | 'system';
  destination_id?: string;
  processed_by: string;
  created_at: string;
  vault_code?: string;
  vault_name?: string;
  processed_by_name?: string;
}

class TreasuryService {
  // Entregar valores para caixa
  async deliverToCash(data: DeliverToCashRequest): Promise<TreasuryDelivery> {
    const response = await makeRequest<{ delivery: TreasuryDelivery }>('/treasury/deliver-to-cash', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data.delivery;
    }
    throw new Error(response.message || 'Erro ao realizar entrega a caixa');
  }

  // Entregar valores para cofre
  async deliverToVault(data: DeliverToVaultRequest): Promise<VaultMovement> {
    const response = await makeRequest<{ movement: VaultMovement }>('/treasury/deliver-to-vault', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data.movement;
    }
    throw new Error(response.message || 'Erro ao realizar entrega ao cofre');
  }

  // Entregar valores para tesoureiro
  async deliverToTreasurer(data: DeliverToTreasurerRequest): Promise<TreasuryDelivery> {
    const response = await makeRequest<{ delivery: TreasuryDelivery }>('/treasury/deliver-to-treasurer', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data.delivery;
    }
    throw new Error(response.message || 'Erro ao realizar entrega ao tesoureiro');
  }

  // Entregar valores para balcão
  async deliverToCounter(data: DeliverToCounterRequest): Promise<TreasuryDelivery> {
    const response = await makeRequest<{ delivery: TreasuryDelivery }>('/treasury/deliver-to-counter', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.data) {
      return response.data.delivery;
    }
    throw new Error(response.message || 'Erro ao realizar entrega ao balcão');
  }

  // Listar entregas da tesouraria
  async getDeliveries(filters: {
    delivery_type?: 'cash_delivery' | 'vault_delivery';
    status?: 'pending' | 'confirmed' | 'cancelled';
    start_date?: string;
    end_date?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<{ deliveries: TreasuryDelivery[]; pagination: any }> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await makeRequest<{ deliveries: TreasuryDelivery[]; pagination: any }>(
      `/treasury/deliveries?${queryParams.toString()}`
    );

    if (response.data) {
      return response.data;
    }
    throw new Error(response.message || 'Erro ao carregar entregas da tesouraria');
  }

  // Calcular total das denominações
  calculateDenominationsTotal(denominations: CashDenominations): number {
    return (
      denominations.notes_10000 * 10000 +
      denominations.notes_5000 * 5000 +
      denominations.notes_2000 * 2000 +
      denominations.notes_1000 * 1000 +
      denominations.notes_500 * 500 +
      denominations.notes_200 * 200 +
      denominations.notes_100 * 100 +
      denominations.notes_50 * 50 +
      denominations.coins_10 * 10 +
      denominations.coins_5 * 5 +
      denominations.coins_1 * 1
    );
  }

  // Formatar valor monetário
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  }

  // Validar denominações
  validateDenominations(denominations: CashDenominations, expectedTotal: number): { isValid: boolean; error?: string } {
    const calculatedTotal = this.calculateDenominationsTotal(denominations);
    
    if (Math.abs(calculatedTotal - expectedTotal) > 0.01) {
      return {
        isValid: false,
        error: `Total das denominações (${this.formatCurrency(calculatedTotal)}) não confere com o valor informado (${this.formatCurrency(expectedTotal)})`
      };
    }
    
    return { isValid: true };
  }

  // Obter denominações vazias
  getEmptyDenominations(): CashDenominations {
    return {
      notes_10000: 0,
      notes_5000: 0,
      notes_2000: 0,
      notes_1000: 0,
      notes_500: 0,
      notes_200: 0,
      notes_100: 0,
      notes_50: 0,
      coins_10: 0,
      coins_5: 0,
      coins_1: 0
    };
  }

  // Obter configuração das denominações para exibição
  getDenominationConfig(): Array<{
    key: keyof CashDenominations;
    label: string;
    value: number;
    type: 'note' | 'coin';
  }> {
    return [
      { key: 'notes_10000', label: '10.000 Kz', value: 10000, type: 'note' },
      { key: 'notes_5000', label: '5.000 Kz', value: 5000, type: 'note' },
      { key: 'notes_2000', label: '2.000 Kz', value: 2000, type: 'note' },
      { key: 'notes_1000', label: '1.000 Kz', value: 1000, type: 'note' },
      { key: 'notes_500', label: '500 Kz', value: 500, type: 'note' },
      { key: 'notes_200', label: '200 Kz', value: 200, type: 'note' },
      { key: 'notes_100', label: '100 Kz', value: 100, type: 'note' },
      { key: 'notes_50', label: '50 Kz', value: 50, type: 'note' },
      { key: 'coins_10', label: '10 Kz', value: 10, type: 'coin' },
      { key: 'coins_5', label: '5 Kz', value: 5, type: 'coin' },
      { key: 'coins_1', label: '1 Kz', value: 1, type: 'coin' }
    ];
  }
}

export const treasuryService = new TreasuryService();
