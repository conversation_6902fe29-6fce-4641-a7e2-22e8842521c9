import { tokenManager } from './tokenManager';

// Configuração da API
// Em desenvolvimento, usar URL relativa para aproveitar o proxy do Vite
// Em produção, usar a URL completa do backend
const API_BASE_URL = import.meta.env.VITE_API_URL || (import.meta.env.DEV ? '' : 'http://localhost:3001');

export const API_CONFIG = {
  baseURL: `${API_BASE_URL}/api`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Função auxiliar para obter headers de autenticação
const getAuthHeaders = (): Record<string, string> => {
  const token = tokenManager.getAccessToken();
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Função auxiliar para fazer requisições HTTP com retry
export const makeRequest = async <T>(
  endpoint: string,
  options: RequestInit = {},
  retries: number = 3,
  delay: number = 1000
): Promise<{ data?: T; status: string; message?: string; success?: boolean }> => {
  const url = `${API_CONFIG.baseURL}${endpoint}`;

  const config: RequestInit = {
    ...options,
    headers: {
      ...API_CONFIG.headers,
      ...getAuthHeaders(),
      ...options.headers,
    },
  };

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Normalizar resposta para formato consistente
      if (data.status === 'success') {
        return {
          data: data.data,
          status: data.status,
          message: data.message,
          success: true
        };
      } else if (data.status === 'error') {
        throw new Error(data.message || 'Erro na resposta da API');
      }
      
      // Para respostas que não seguem o padrão, assumir sucesso
      return {
        data: data,
        status: 'success',
        success: true
      };
    } catch (error: any) {
      console.error(`Tentativa ${attempt} falhou:`, error);
      
      if (attempt === retries) {
        throw error;
      }
      
      // Aguardar antes da próxima tentativa
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw new Error('Falha após múltiplas tentativas');
};

// Função auxiliar para fazer requisições simples (sem retry)
export const simpleRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const url = `${API_CONFIG.baseURL}${endpoint}`;

  const config: RequestInit = {
    ...options,
    headers: {
      ...API_CONFIG.headers,
      ...getAuthHeaders(),
      ...options.headers,
    },
  };

  const response = await fetch(url, config);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
};

// Função para upload de arquivos
export const uploadFile = async (
  endpoint: string,
  file: File,
  additionalData?: Record<string, any>
): Promise<any> => {
  const url = `${API_CONFIG.baseURL}${endpoint}`;
  
  const formData = new FormData();
  formData.append('file', file);
  
  if (additionalData) {
    Object.entries(additionalData).forEach(([key, value]) => {
      formData.append(key, typeof value === 'string' ? value : JSON.stringify(value));
    });
  }

  const config: RequestInit = {
    method: 'POST',
    headers: {
      ...getAuthHeaders(),
      // Não definir Content-Type para FormData - o browser define automaticamente
    },
    body: formData,
  };

  const response = await fetch(url, config);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
};

// Função para download de arquivos
export const downloadFile = async (
  endpoint: string,
  filename?: string
): Promise<void> => {
  const url = `${API_CONFIG.baseURL}${endpoint}`;

  const config: RequestInit = {
    method: 'GET',
    headers: {
      ...getAuthHeaders(),
    },
  };

  const response = await fetch(url, config);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
  }

  const blob = await response.blob();
  const downloadUrl = window.URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = filename || 'download';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  window.URL.revokeObjectURL(downloadUrl);
};

// Tipos para respostas da API
export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  message?: string;
  data?: T;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Constantes de endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
  },
  USERS: {
    LIST: '/users',
    CREATE: '/users',
    GET: (id: string) => `/users/${id}`,
    UPDATE: (id: string) => `/users/${id}`,
    DELETE: (id: string) => `/users/${id}`,
    CASHIERS: '/users/cashiers',
  },
  BRANCHES: {
    LIST: '/branches',
    CREATE: '/branches',
    GET: (id: string) => `/branches/${id}`,
    UPDATE: (id: string) => `/branches/${id}`,
    DELETE: (id: string) => `/branches/${id}`,
  },
  ROLES: {
    LIST: '/roles',
    CREATE: '/roles',
    GET: (id: string) => `/roles/${id}`,
    UPDATE: (id: string) => `/roles/${id}`,
    DELETE: (id: string) => `/roles/${id}`,
  },
  CASH_REGISTERS: {
    LIST: '/cash-registers',
    CREATE: '/cash-registers',
    GET: (id: string) => `/cash-registers/${id}`,
    UPDATE: (id: string) => `/cash-registers/${id}`,
    DELETE: (id: string) => `/cash-registers/${id}`,
    AVAILABLE: '/cash-registers/available',
    SESSIONS: {
      OPEN: '/cash-registers/sessions/open',
      CLOSE: '/cash-registers/sessions/close',
      CURRENT: '/cash-registers/sessions/current',
      LIST: '/cash-registers/sessions',
    },
  },
  TREASURY: {
    DELIVER_TO_CASH: '/treasury/deliver-to-cash',
    DELIVER_TO_VAULT: '/treasury/deliver-to-vault',
    DELIVERIES: '/treasury/deliveries',
  },
  SYSTEM: {
    VAULT_BALANCE: '/system/vault/balance',
    VAULT_INITIAL_BALANCE: '/system/vault/initial-balance',
    VAULT_HISTORY: '/system/vault/history',
    SETTINGS: '/system/settings',
    VAULTS: '/system/vaults',
    VAULT_BY_ID: '/system/vaults',
    VAULT_MOVEMENTS: '/system/vaults',
    VAULT_STATISTICS: '/system/vaults',
  },
};
